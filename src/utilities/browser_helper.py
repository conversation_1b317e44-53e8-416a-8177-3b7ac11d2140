import os
import logging
import re
from typing import Optional, Any, List, Union, Dict
from pathlib import Path
from datetime import datetime
import shutil
import glob
import time

# Set up logger first
logger = logging.getLogger(__name__)

# Configurar path para browser_use local
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../libs'))

# Browser-use imports with proper error handling
try:
    from browser_use import Agent as BrowserAgent, BrowserSession, BrowserProfile
    # Note: Memory system removed in browser-use v0.5.0
    # from browser_use.agent.memory import MemoryConfig, Memory  # REMOVED
    BROWSER_USE_AVAILABLE = True
    MEMORY_CONFIG_AVAILABLE = False  # Memory system removed in v0.5.0
    
    # Memory dependencies are no longer needed
    MEMORY_DEPS_VERIFIED = False  # Memory system removed in v0.5.0
        
except ImportError as e:
    logger.error(f"Failed to import browser-use components: {e}")
    BROWSER_USE_AVAILABLE = False
    MEMORY_CONFIG_AVAILABLE = False
    MEMORY_DEPS_VERIFIED = False
    
    # Create dummy classes for fallback
    class BrowserAgent:
        pass
    class BrowserSession:
        pass
    class BrowserProfile:
        pass

# LLM imports - Using new LLM service architecture
try:
    from src.services.llm.llm_service_factory import LLMServiceFactory
    from src.services.llm.base_llm_service import LLMRequest, LLMResponse
    LLM_SERVICE_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import new LLM service: {e}")
    LLM_SERVICE_AVAILABLE = False

# Legacy LLM imports for browser-use compatibility
try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    GOOGLE_GENAI_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import ChatGoogleGenerativeAI: {e}")
    GOOGLE_GENAI_AVAILABLE = False
    ChatGoogleGenerativeAI = None

# Optional LLM imports
try:
    from langchain_openai import ChatOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from langchain_anthropic import ChatAnthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

# Import prompt service
from src.core.prompt_service import PromptService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BrowserHelperConfig:
    """Enhanced configuration class for browser helper settings."""

    def __init__(
        self,
        # Browser settings
        headless: Optional[bool] = None,
        user_data_dir: Optional[str] = None,
        allowed_domains: Optional[List[str]] = None,
        disable_security: bool = False,
        deterministic_rendering: bool = False,
        highlight_elements: bool = True,
        viewport_expansion: int = 500,

        # Performance settings
        minimum_wait_page_load_time: float = 0.5,
        wait_for_network_idle_page_load_time: float = 1.0,
        maximum_wait_page_load_time: float = 10.0,
        wait_between_actions: float = 0.5,

        # Agent settings
        max_steps: int = 100,
        max_failures: int = 3,
        retry_delay: float = 5.0,
        use_vision: bool = True,
        enable_memory: bool = False,  # DEPRECATED in browser-use v0.5.0+
        save_conversation_path: Optional[str] = None,
        generate_gif: bool = False,

        # Browser-use memory settings (MemoryConfig compatible)
        memory_agent_id: Optional[str] = None,
        memory_interval: int = 10,

        # Embedding settings for memory (new)
        embedder_provider: Optional[str] = None,
        embedder_model: Optional[str] = None,
        embedder_dims: Optional[int] = None,
        vector_store_provider: str = "chroma",
        vector_store_base_path: Optional[str] = None,

        # Planner settings (browser-use 0.2.5+ compatible)
        planner_llm: Optional[Any] = None,
        use_vision_for_planner: bool = True,
        planner_interval: int = 1,

        # Initial actions for optimization
        initial_actions: Optional[List[Dict[str, Any]]] = None,

        # Browser session settings
        keep_alive: bool = False,
        storage_state: Optional[str] = None,

        # Model settings
        model_provider: Optional[str] = None,  # Auto-detect from environment if not specified
        model_name: Optional[str] = None,
        temperature: float = 0.0,
        tool_calling_method: str | None = None,

        # Viewport settings
        viewport: Optional[Dict[str, int]] = None,
        device_scale_factor: Optional[float] = None,

        # Recording settings
        record_video_dir: Optional[str] = None,
        trace_path: Optional[str] = None,

        # Language settings (simplified approach)
        response_language: Optional[str] = None,  # Language for AI responses
        
        # Legacy language parameter (for backward compatibility)
        language: Optional[str] = None,

        # Additional kwargs for future compatibility
        **kwargs
    ):
        # Browser settings
        self.headless = headless
        self.user_data_dir = user_data_dir
        self.allowed_domains = allowed_domains
        self.disable_security = disable_security
        self.deterministic_rendering = deterministic_rendering
        self.highlight_elements = highlight_elements
        self.viewport_expansion = viewport_expansion

        # Performance settings
        self.minimum_wait_page_load_time = minimum_wait_page_load_time
        self.wait_for_network_idle_page_load_time = wait_for_network_idle_page_load_time
        self.maximum_wait_page_load_time = maximum_wait_page_load_time
        self.wait_between_actions = wait_between_actions

        # Agent settings
        self.max_steps = max_steps
        self.max_failures = max_failures
        self.retry_delay = retry_delay
        self.use_vision = use_vision
        self.enable_memory = enable_memory  # DEPRECATED in browser-use v0.5.0+
        self.save_conversation_path = save_conversation_path
        self.generate_gif = generate_gif

        # Browser-use memory settings
        self.memory_agent_id = memory_agent_id
        self.memory_interval = memory_interval

        # Embedding settings for memory
        self.embedder_provider = embedder_provider
        self.embedder_model = embedder_model
        self.embedder_dims = embedder_dims
        self.vector_store_provider = vector_store_provider
        self.vector_store_base_path = vector_store_base_path

        # Planner settings
        self.planner_llm = planner_llm
        self.use_vision_for_planner = use_vision_for_planner
        self.planner_interval = planner_interval

        # Initial actions
        self.initial_actions = initial_actions

        # Browser session settings
        self.keep_alive = keep_alive
        self.storage_state = storage_state

        # Model settings
        self.model_provider = model_provider
        self.model_name = model_name
        self.temperature = temperature
        # Tool calling method
        if tool_calling_method is not None:
            self.tool_calling_method = tool_calling_method
        else:
            # Default heuristic: Use function_calling for all models for now
            # Previous json_mode default for Gemini 2.5* was causing MALFORMED_FUNCTION_CALL errors
            default_method = "function_calling"  # Changed from json_mode for Gemini 2.5
            self.tool_calling_method = default_method

        # Viewport settings
        self.viewport = viewport or {"width": 1280, "height": 720}
        self.device_scale_factor = device_scale_factor

        # Recording settings
        self.record_video_dir = record_video_dir
        self.trace_path = trace_path

        # Language settings (simplified approach)
        self.response_language = response_language
        
        # Legacy language parameter (for backward compatibility)
        self.language = language

        # Store any additional parameters for future compatibility
        for key, value in kwargs.items():
            setattr(self, key, value)

        # expose in __dict__ for easier introspection
        self._extra_kwargs = kwargs


def create_llm_instance(config: BrowserHelperConfig, api_key: str):
    """Create LLM instance based on provider configuration.

    Note: This function still uses legacy ChatGoogleGenerativeAI for browser-use compatibility.
    The browser-use library requires specific LangChain model instances and is excluded from
    the OpenRouter migration per project requirements.
    """

    # Auto-detect provider if not specified
    if not config.model_provider:
        from src.config.browser_config import BrowserConfigurations
        config.model_provider = BrowserConfigurations.get_model_provider_from_env()

    if not GOOGLE_GENAI_AVAILABLE and config.model_provider.lower() == "gemini":
        raise RuntimeError("ChatGoogleGenerativeAI not available. Please install langchain-google-genai.")

    if config.model_provider.lower() == "gemini":
        # Usar modelos más estables por defecto y con configuración robusta
        model_name = config.model_name or os.getenv("LLM_MODEL", "gemini-1.5-flash")
        
        # Configuración robusta para Google AI
        llm_config = {
            "model": model_name,
            "api_key": api_key,
            "temperature": config.temperature,
            "timeout": 80,  # Timeout más conservador
            "max_retries": 3,
            "max_tokens": 4096,
            "convert_system_message_to_human": True,  # Mejor compatibilidad
        }

        # Intentar modelos de respaldo si el principal falla
        models_to_try = [model_name]
        if model_name not in ["gemini-2.5-flash", "gemini-2.5-flash"]:
            models_to_try.extend(["gemini-2.5-flash", "gemini-2.5-flash"])
        
        last_error = None
        for model_attempt in models_to_try:
            try:
                llm_config["model"] = model_attempt
                llm = ChatGoogleGenerativeAI(**llm_config)
                
                # Test básico de la instancia LLM (sin async call)
                if hasattr(llm, 'model'):
                    logger.info(f"Google AI LLM created successfully with model: {model_attempt}")
                    return llm
                    
            except Exception as e:
                last_error = e
                logger.warning(f"Failed to create LLM with model {model_attempt}: {e}")
                continue
        
        # Si todos los modelos fallan, lanzar el último error
        if last_error:
            raise ValueError(f"Failed to create Google AI LLM after trying multiple models: {last_error}")
        
        return ChatGoogleGenerativeAI(**llm_config)

    elif config.model_provider.lower() == "openai":
        if not OPENAI_AVAILABLE:
            raise ValueError("OpenAI not available. Install langchain-openai: pip install langchain-openai")

        model_name = config.model_name or "gpt-4o"
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable not found")
        return ChatOpenAI(
            model=model_name,
            api_key=openai_api_key,
            temperature=config.temperature
        )

    elif config.model_provider.lower() == "anthropic":
        if not ANTHROPIC_AVAILABLE:
            raise ValueError("Anthropic not available. Install langchain-anthropic: pip install langchain-anthropic")

        model_name = config.model_name or "claude-3-5-sonnet-20240620"
        anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        if not anthropic_api_key:
            raise ValueError("ANTHROPIC_API_KEY environment variable not found")
        return ChatAnthropic(
            model_name=model_name,
            api_key=anthropic_api_key,
            temperature=config.temperature
        )

    elif config.model_provider.lower() == "openrouter":
        # Use browser-use's ChatOpenRouter for compatibility with browser-use message system
        if not BROWSER_USE_AVAILABLE:
            raise ValueError("browser-use not available. Install browser-use>=0.5.0")

        model_name = config.model_name or os.getenv("BROWSER_USE_MODEL_NAME") or os.getenv("LLM_MODEL", "openai/gpt-4o-mini")
        openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        if not openrouter_api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable not found")
        
        logger.info(f"Creating OpenRouter LLM with model: {model_name}")
        
        # Import browser-use's ChatOpenRouter
        from browser_use.llm.openrouter.chat import ChatOpenRouter
        
        return ChatOpenRouter(
            model=model_name,
            api_key=openrouter_api_key,
            temperature=config.temperature,
            timeout=80,
            max_retries=3
        )

    else:
        raise ValueError(f"Unsupported model provider: {config.model_provider}")


def create_browser_profile(config: BrowserHelperConfig) -> BrowserProfile:
    """Create BrowserProfile from configuration."""

    if not BROWSER_USE_AVAILABLE:
        raise RuntimeError("browser-use not available. Please install browser-use>=0.2.4")

    # Auto-detect headless mode if not specified
    headless = config.headless
    if headless is None:
        headless = os.getenv("DISPLAY") is None and os.getenv("WAYLAND_DISPLAY") is None

    # Handle user_data_dir to prevent SingletonLock conflicts
    user_data_dir = config.user_data_dir
    if user_data_dir is None:
        # Use None for ephemeral/incognito profiles to avoid SingletonLock conflicts
        # This is the recommended approach for parallel sessions
        user_data_dir = None
    elif user_data_dir == "~/.config/browseruse/profiles/agentqa_profile":
        # Generate unique directory for each session to prevent conflicts
        import uuid
        import time
        session_id = f"agentqa_{int(time.time())}_{str(uuid.uuid4())[:8]}"
        user_data_dir = f"~/.config/browseruse/profiles/{session_id}"
        logger.info(f"Using unique profile directory: {user_data_dir}")

    # Build BrowserProfile arguments
    profile_args = {
        "headless": headless,
        "user_data_dir": user_data_dir,
        "allowed_domains": config.allowed_domains,
        "disable_security": config.disable_security,
        "deterministic_rendering": config.deterministic_rendering,
        "highlight_elements": config.highlight_elements,
        "viewport_expansion": config.viewport_expansion,
        "minimum_wait_page_load_time": config.minimum_wait_page_load_time,
        "wait_for_network_idle_page_load_time": config.wait_for_network_idle_page_load_time,
        "maximum_wait_page_load_time": config.maximum_wait_page_load_time,
        "wait_between_actions": config.wait_between_actions,
        "keep_alive": config.keep_alive,
    }

    # Add optional parameters if they exist
    if config.storage_state:
        profile_args["storage_state"] = config.storage_state

    if config.viewport:
        profile_args["viewport"] = config.viewport

    if config.device_scale_factor:
        profile_args["device_scale_factor"] = config.device_scale_factor

    if config.record_video_dir:
        profile_args["record_video_dir"] = config.record_video_dir

    if config.trace_path:
        profile_args["trace_path"] = config.trace_path

    # Remove None values except for user_data_dir which needs to be explicitly None for ephemeral profiles
    profile_args = {k: v for k, v in profile_args.items() if v is not None or k == "user_data_dir"}

    return BrowserProfile(**profile_args)


def extract_urls_from_scenario(scenario_text: str) -> List[str]:
    """Extract URLs from Gherkin scenario text."""
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    return url_pattern.findall(scenario_text)


def create_initial_actions_from_url(url: str) -> List[Dict[str, Any]]:
    """Create initial actions to navigate to a URL."""
    if not url:
        return []

    return [
        {'go_to_url': {'url': url, 'new_tab': True}}
    ]


async def create_and_run_agent(
    scenario_text: str,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,
    config: Optional[BrowserHelperConfig] = None,
    url: Optional[str] = None,
    browser_session: Optional[BrowserSession] = None,
    token_optimized: bool = False
) -> Any:
    """
    Enhanced browser agent creation and execution with consistent English instructions.

    Args:
        scenario_text: The Gherkin scenario to execute
        controller_instance: Browser controller instance
        api_key: API key for the LLM (defaults to environment variable)
        language: Language for responses ('en' or 'es') - instructions always in English
        config: BrowserHelperConfig instance for advanced configuration
        url: URL for direct navigation (takes precedence over URL extraction from scenario)
        browser_session: Existing browser session to reuse
        token_optimized: Legacy parameter (ignored)

    Returns:
        Agent execution history

    Raises:
        ValueError: If required API keys are missing
        Exception: If agent execution fails
    """

    if not BROWSER_USE_AVAILABLE:
        raise RuntimeError("browser-use not available. Please install browser-use>=0.2.4")

    # Use default config if none provided
    if config is None:
        config = BrowserHelperConfig()

    # Validate and set API key
    if api_key is None:
        if config.model_provider.lower() == "gemini":
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY environment variable not found. Please set it or pass an api_key.")
        elif config.model_provider.lower() == "openai":
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable not found.")
        elif config.model_provider.lower() == "anthropic":
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key:
                raise ValueError("ANTHROPIC_API_KEY environment variable not found.")

    # Process language parameters - simplified approach
    # Instructions are always in English, only response language varies
    effective_response_language = (
        config.response_language if config.response_language is not None
        else language if language is not None 
        else os.getenv("PROMPT_LANGUAGE", "en")
    )
    effective_instruction_language = "en"  # Always default to English for instructions
    
    # Determine if translation is needed
    translation_enabled = (
        hasattr(config, 'enable_response_translation') and config.enable_response_translation
    )
    needs_translation = (
        translation_enabled and 
        effective_response_language != "en" and 
        effective_instruction_language == "en"
    )
    
    logger.info(f"Language configuration - Instructions: {effective_instruction_language}, Response: {effective_response_language}, Translation: {needs_translation}")

    # Generate agent task with enhanced prompts using PromptService
    # Always use English for internal instructions
    prompt_service = PromptService()

    # Generate URL preservation instructions
    import re
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    scenario_urls = url_pattern.findall(scenario_text)

    # Add URL parameter if provided
    all_urls = list(scenario_urls)
    if url and url not in all_urls:
        all_urls.append(url)

    # Create URL preservation instructions (always in English for consistency)
    if all_urls:
        # Commented out original URL preservation instructions
        # url_preservation_instructions = """
        # **CRITICAL URL PRESERVATION INSTRUCTION:**
        # You MUST use the exact URLs as provided in the scenario without any modification. Do not change, abbreviate, or omit any part of the URLs.
        # The following URLs must be preserved exactly as written:
        # """
        # Commented out original URL preservation instructions para testear como se comporta
        url_preservation_instructions = ""
        for url_item in all_urls:
            url_preservation_instructions += f"    - {url_item}\n"
    else:
        url_preservation_instructions = ""

    agent_task = prompt_service.generate_browser_task(
        scenario_text,
        "en",  # Always use English for instructions, regardless of response language
        url_preservation_instructions=url_preservation_instructions
    )

    logger.info(f"Creating browser agent with provider: {config.model_provider}")

    try:
        # Create LLM instance
        llm_instance = create_llm_instance(config, api_key)

        # Use provided browser session or create a new one
        session_managed_externally = browser_session is not None
        if not session_managed_externally:
            browser_profile = create_browser_profile(config)
            browser_session = BrowserSession(
                browser_profile=browser_profile,
                keep_alive=config.keep_alive
            )

        # Set up conversation saving if requested
        conversation_path = None
        if config.save_conversation_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            conversation_path = f"{config.save_conversation_path}/conversation_{timestamp}.json"
            os.makedirs(os.path.dirname(conversation_path), exist_ok=True)

        # Create initial actions based on URL parameter or extract from scenario
        initial_actions = config.initial_actions
        if initial_actions is None:
            # Priority: 1) URL parameter, 2) Extract from scenario
            target_url = url
            if not target_url:
                urls = extract_urls_from_scenario(scenario_text)
                target_url = urls[0] if urls else None

            if target_url:
                initial_actions = create_initial_actions_from_url(target_url)
                logger.info(f"Auto-generated initial actions for URL: {target_url}")
            else:
                logger.info("No URL provided or found in scenario - no initial actions generated")

        # Determine tool calling method based on actual model name
        tool_calling_method = getattr(config, "tool_calling_method", "auto")
        if tool_calling_method == "auto" or tool_calling_method is None:
            # Check the actual model name from LLM instance
            actual_model_name = getattr(llm_instance, 'model', getattr(llm_instance, 'model_name', ''))
            if str(actual_model_name).lower().startswith("gemini-2.5"):
                # Changed: Use function_calling instead of json_mode for Gemini 2.5
                # json_mode was causing MALFORMED_FUNCTION_CALL errors
                tool_calling_method = "function_calling"
                logger.info(f"Auto-detected Gemini 2.5* model, using function_calling for tool calling")
            else:
                tool_calling_method = "function_calling"

        # Configure browser agent with enhanced settings
        agent_kwargs = {
            "task": agent_task,
            "llm": llm_instance,
            "controller": controller_instance,
            "browser_session": browser_session,
            "use_vision": config.use_vision,
            "tool_calling_method": tool_calling_method,
            "calculate_cost": False,  # Disable cost tracking to avoid compatibility issues
        }

        # Configure browser-use settings (Memory system removed in v0.5.0)
        # Note: Memory functionality was removed in browser-use v0.5.0
        # The agent context for memory is significantly improved and no longer requires the old memory system
        if hasattr(config, 'enable_memory') and config.enable_memory:
            logger.warning(
                "Memory support has been removed as of browser-use version 0.5.0. "
                "The agent context for memory is significantly improved and no longer requires the old memory system. "
                "Ignoring enable_memory parameter."
            )
        
        # Memory configuration is no longer available - removed section
        agent_kwargs["enable_memory"] = False  # Always False in v0.5.0+

        # Add planner configuration if planner_llm is provided
        if config.planner_llm:
            agent_kwargs["planner_llm"] = config.planner_llm
            agent_kwargs["use_vision_for_planner"] = config.use_vision_for_planner
            agent_kwargs["planner_interval"] = config.planner_interval
            logger.info("Planner enabled with custom LLM")

        # Add initial actions if available
        if initial_actions:
            agent_kwargs["initial_actions"] = initial_actions
            logger.info(f"Using {len(initial_actions)} initial actions")

        # Add conversation saving if configured
        if conversation_path:
            agent_kwargs["save_conversation_path"] = conversation_path

        # Add unique task_id to prevent EventBus name conflicts
        import uuid
        unique_task_id = str(uuid.uuid4())[:8]  # Short unique ID
        agent_kwargs["task_id"] = unique_task_id

        # Create the agent
        browser_agent_instance = BrowserAgent(**agent_kwargs)

        logger.info("Starting browser agent execution...")
        logger.info(f"Using model provider: {config.model_provider}")
        logger.info(f"Vision enabled: {config.use_vision}")
        logger.info(f"Max steps: {config.max_steps}")

        # Execute the agent
        history = await browser_agent_instance.run(max_steps=config.max_steps)

        logger.info("Browser agent execution completed successfully")

        # Log execution summary with safe attribute access
        try:
            if hasattr(history, 'action_names') and callable(getattr(history, 'action_names')):
                logger.info(f"Executed {len(history.action_names())} actions")
            if hasattr(history, 'urls') and callable(getattr(history, 'urls')):
                logger.info(f"Visited {len(history.urls())} URLs")
            if hasattr(history, 'errors') and callable(getattr(history, 'errors')) and history.errors():
                logger.warning(f"Encountered {len(history.errors())} errors during execution")
        except Exception as e:
            logger.debug(f"Could not log execution summary: {e}")

        # Close session only if it was created inside this function
        if not session_managed_externally and browser_session and not config.keep_alive:
            await browser_session.close()
            logger.info("Browser session closed as it was managed internally.")

        return history

    except Exception as e:
        logger.error(f"Browser agent execution failed: {str(e)}")
        raise


# Utility functions for creating common configurations

def create_fast_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for speed and minimal resource usage (smoke tests)."""
    defaults = {
        "headless": True,
        "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
        "use_vision": True,  # ✅ Habilitado para smoke tests
        "enable_memory": False,  # ✅ Memory removed in browser-use v0.5.0
        "max_steps": 30,
        "minimum_wait_page_load_time": 0.3,
        "wait_for_network_idle_page_load_time": 0.5,
        "maximum_wait_page_load_time": 8.0,
        "wait_between_actions": 0.2,
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 300,
        "deterministic_rendering": False,  # Recomendado: evita detección anti-bot
        "highlight_elements": False,  # Disable for speed
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_robust_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for reliability and comprehensive testing."""
    defaults = {
        "headless": True,
        "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
        "use_vision": True,
        "enable_memory": False,  # ✅ Memory removed in browser-use v0.5.0
        "memory_interval": 4,  # Resumir cada 10 pasos para full tests (balance entre eficiencia y memoria)
        "max_steps": 150,
        "minimum_wait_page_load_time": 0.8,
        "wait_for_network_idle_page_load_time": 1.5,
        "maximum_wait_page_load_time": 15.0,
        "wait_between_actions": 0.8,
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 800,
        "deterministic_rendering": False,  # Recomendado: evita detección anti-bot
        "highlight_elements": True
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_secure_config(allowed_domains: List[str], **kwargs) -> BrowserHelperConfig:
    """Create a configuration with security restrictions for production use."""
    defaults = {
        "headless": True,
        "user_data_dir": None,  # Usar perfil temporal para evitar conflictos de SingletonLock
        "allowed_domains": allowed_domains,
        "disable_security": False,
        "use_vision": True,
        "max_steps": 100,
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 500,
        "deterministic_rendering": False,  # Recomendado: evita detección anti-bot
        "highlight_elements": False,  # Disable for production
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def validate_config(config: BrowserHelperConfig) -> List[str]:
    """
    Valida la configuración de BrowserHelperConfig y devuelve advertencias.

    Args:
        config: Configuración a validar

    Returns:
        List[str]: Lista de advertencias (vacía si no hay problemas)
    """
    warnings = []

    # Validar configuración de memoria
    if config.enable_memory:
        if not MEMORY_CONFIG_AVAILABLE:
            warnings.append("Memory enabled but MemoryConfig not available. Install browser-use>=0.2.4")

        if not hasattr(config, 'memory_interval') or config.memory_interval <= 0:
            warnings.append("memory_interval should be greater than 0")
        
        if hasattr(config, 'memory_interval') and hasattr(config, 'max_steps') and config.memory_interval > config.max_steps:
            warnings.append("memory_interval is greater than max_steps - memory won't be used effectively")

        if not hasattr(config, 'memory_agent_id') or not config.memory_agent_id:
            warnings.append("memory_agent_id not set - using default agent ID")

    # Validar configuración de pasos
    if config.max_steps <= 0:
        warnings.append("max_steps should be greater than 0")

    # Validar configuración de tiempos
    if config.minimum_wait_page_load_time < 0:
        warnings.append("minimum_wait_page_load_time should be non-negative")

    if config.wait_for_network_idle_page_load_time < config.minimum_wait_page_load_time:
        warnings.append("wait_for_network_idle_page_load_time should be >= minimum_wait_page_load_time")

    if config.maximum_wait_page_load_time < config.wait_for_network_idle_page_load_time:
        warnings.append("maximum_wait_page_load_time should be >= wait_for_network_idle_page_load_time")

    # Validar configuración de visión  
    # Note: Memory functionality was removed in browser-use v0.5.0
    if not config.use_vision and hasattr(config, 'enable_memory') and config.enable_memory:
        warnings.append("Memory support has been removed in browser-use v0.5.0")

    return warnings


def create_debug_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for debugging and development."""
    defaults = {
        "headless": False,
        "use_vision": True,
        "enable_memory": False,  # Memory removed in browser-use v0.5.0
        "memory_interval": 3,  # Resumir cada 3 pasos para debugging (muy frecuente)
        "max_steps": 50,
        "minimum_wait_page_load_time": 1.0,
        "wait_for_network_idle_page_load_time": 2.0,
        "maximum_wait_page_load_time": 20.0,
        "wait_between_actions": 2.0,  # Slower for observation
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 1000,
        "deterministic_rendering": False,  # Recomendado: evita detección anti-bot
        "highlight_elements": True,
        "save_conversation_path": "./debug_conversations"
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def cleanup_orphaned_profiles(profile_base_dir: str = "~/.config/browseruse/profiles", 
                             max_age_hours: int = 24) -> List[str]:
    """
    Limpia perfiles de navegador huérfanos que puedan tener SingletonLock bloqueado.
    
    Args:
        profile_base_dir: Directorio base de perfiles
        max_age_hours: Edad máxima en horas para considerar un perfil huérfano
        
    Returns:
        List[str]: Lista de directorios limpiados
    """
    cleaned_dirs = []
    
    try:
        expanded_dir = os.path.expanduser(profile_base_dir)
        if not os.path.exists(expanded_dir):
            return cleaned_dirs
            
        # Buscar perfiles temporales con patrón agentqa_*
        pattern = os.path.join(expanded_dir, "agentqa_*")
        profile_dirs = glob.glob(pattern)
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        for profile_dir in profile_dirs:
            try:
                # Verificar la edad del directorio
                dir_mtime = os.path.getmtime(profile_dir)
                if current_time - dir_mtime > max_age_seconds:
                    # Verificar si hay un archivo SingletonLock
                    singleton_lock = os.path.join(profile_dir, "SingletonLock")
                    if os.path.exists(singleton_lock):
                        logger.info(f"Removing orphaned profile with SingletonLock: {profile_dir}")
                        shutil.rmtree(profile_dir, ignore_errors=True)
                        cleaned_dirs.append(profile_dir)
                    elif os.path.isdir(profile_dir):
                        # Remover directorio huérfano sin SingletonLock también
                        logger.info(f"Removing orphaned profile directory: {profile_dir}")
                        shutil.rmtree(profile_dir, ignore_errors=True)
                        cleaned_dirs.append(profile_dir)
            except OSError as e:
                logger.warning(f"Could not clean profile directory {profile_dir}: {e}")
                
    except Exception as e:
        logger.error(f"Error during profile cleanup: {e}")
        
    return cleaned_dirs


def remove_singleton_lock(user_data_dir: str) -> bool:
    """
    Remueve un archivo SingletonLock específico de un directorio de perfil.
    
    Args:
        user_data_dir: Directorio del perfil de usuario
        
    Returns:
        bool: True si se removió exitosamente o no existía, False si hubo error
    """
    try:
        expanded_dir = os.path.expanduser(user_data_dir)
        singleton_lock = os.path.join(expanded_dir, "SingletonLock")
        
        if os.path.exists(singleton_lock):
            os.remove(singleton_lock)
            logger.info(f"Removed SingletonLock from {expanded_dir}")
            return True
        else:
            logger.debug(f"No SingletonLock found in {expanded_dir}")
            return True
            
    except OSError as e:
        logger.error(f"Could not remove SingletonLock from {user_data_dir}: {e}")
        return False


def get_browser_session_with_cleanup(config: BrowserHelperConfig, 
                                   auto_cleanup: bool = True) -> 'BrowserSession':
    """
    Crea una BrowserSession con limpieza automática de profiles huérfanos.
    
    Args:
        config: Configuración del browser
        auto_cleanup: Si debe limpiar perfiles huérfanos automáticamente
        
    Returns:
        BrowserSession: Sesión de navegador configurada
        
    Raises:
        RuntimeError: Si browser-use no está disponible
    """
    if not BROWSER_USE_AVAILABLE:
        raise RuntimeError("browser-use not available. Please install browser-use>=0.2.4")
    
    # Limpiar perfiles huérfanos si está habilitado
    if auto_cleanup:
        try:
            cleaned = cleanup_orphaned_profiles()
            if cleaned:
                logger.info(f"Cleaned {len(cleaned)} orphaned browser profiles")
        except Exception as e:
            logger.warning(f"Profile cleanup failed: {e}")
    
    # Si se está usando un user_data_dir específico, intentar limpiar SingletonLock
    if hasattr(config, 'user_data_dir') and config.user_data_dir and config.user_data_dir != "None":
        remove_singleton_lock(config.user_data_dir)
    
    # Crear la sesión de navegador
    browser_profile = create_browser_profile(config)
    return BrowserSession(browser_profile=browser_profile, keep_alive=config.keep_alive)

# ...existing code...
# Export main functions and classes
__all__ = [
    'BrowserHelperConfig',
    'create_and_run_agent',
    'create_fast_config',
    'create_robust_config',
    'create_secure_config',
    'create_debug_config',
    'validate_config',
    'create_llm_instance',
    'create_browser_profile',
    'extract_urls_from_scenario',
    'create_initial_actions_from_url',
    'cleanup_orphaned_profiles',
    'remove_singleton_lock',
    'get_browser_session_with_cleanup'
]
