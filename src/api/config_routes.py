import logging
"""
Rutas API para gestión de configuraciones de pruebas.
"""

import os
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from src.api.models import (
    TestConfigurationRequest,
    TestConfigurationResponse,
    TestConfigurationCreateRequest,
    TestConfigurationUpdateRequest,
    PredefinedConfigResponse
)
from src.services.browser_configuration_service import BrowserConfigurationService
from src.services.default_configuration_service import DefaultConfigurationService
from src.database.models.browser_configuration import BrowserConfiguration

from src.config.browser_config import (
    BrowserConfigurations,
    get_config_by_type,
    BrowserHelperConfig
)

from src.utilities.browser_helper import (
    validate_config,
    create_fast_config,
    create_robust_config,
    create_secure_config,
    create_debug_config
)

router = APIRouter(tags=["Configuration"])

# Directorios para almacenar configuraciones
CONFIG_DIR = Path("config/custom")
CONFIG_DIR.mkdir(parents=True, exist_ok=True)

PREDEFINED_MODIFIED_DIR = Path("config/predefined_modified")
PREDEFINED_MODIFIED_DIR.mkdir(parents=True, exist_ok=True)

# Dependency para obtener el servicio de configuraciones
def get_browser_config_service() -> BrowserConfigurationService:
    return BrowserConfigurationService()

def get_default_config_service() -> DefaultConfigurationService:
    """Dependency to get DefaultConfigurationService instance."""
    return DefaultConfigurationService()


def _get_env_defaults() -> Dict[str, Any]:
    """Obtiene los valores por defecto desde las variables de entorno."""
    from src.config.browser_config import BrowserConfigurations

    return {
        "model_name": os.getenv("LLM_MODEL", "gemini-2.5-flash"),
        "embedder_model": os.getenv("EMBEDDING_MODEL", "models/text-embedding-004"),
        "embedder_provider": os.getenv("EMBEDDING_PROVIDER", "gemini"),
        "embedder_dims": int(os.getenv("EMBEDDING_DIMS", "768")),
        "model_provider": BrowserConfigurations.get_model_provider_from_env(),
        "temperature": 0.1,
        "memory_agent_id": "browser_use_agent",
    }


def _apply_env_defaults(config_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Aplica valores por defecto del .env a una configuración si no están presentes."""
    env_defaults = _get_env_defaults()

    # Aplicar defaults solo si el valor no existe o es None
    for key, default_value in env_defaults.items():
        if key not in config_dict or config_dict[key] is None:
            config_dict[key] = default_value

    # Siempre sobrescribir model_provider con el valor del entorno
    config_dict["model_provider"] = env_defaults["model_provider"]

    return config_dict


def _browser_helper_config_to_dict(config: BrowserHelperConfig) -> Dict[str, Any]:
    """Convierte una instancia de BrowserHelperConfig a diccionario."""
    return {
        # Configuración del navegador
        "headless": config.headless,
        "user_data_dir": config.user_data_dir,
        "allowed_domains": config.allowed_domains,
        "disable_security": config.disable_security,
        "deterministic_rendering": config.deterministic_rendering,
        "highlight_elements": config.highlight_elements,
        "viewport_expansion": config.viewport_expansion,
        
        # Configuración de rendimiento
        "minimum_wait_page_load_time": config.minimum_wait_page_load_time,
        "wait_for_network_idle_page_load_time": config.wait_for_network_idle_page_load_time,
        "maximum_wait_page_load_time": config.maximum_wait_page_load_time,
        "wait_between_actions": config.wait_between_actions,
        
        # Configuración del agente
        "max_steps": config.max_steps,
        "max_failures": config.max_failures,
        "retry_delay": config.retry_delay,
        "use_vision": config.use_vision,
        "enable_memory": config.enable_memory,
        "save_conversation_path": config.save_conversation_path,
        "generate_gif": config.generate_gif,
        
        # Configuración de memoria
        "memory_agent_id": config.memory_agent_id,
        "memory_interval": config.memory_interval,
        
        # Configuración de embeddings
        "embedder_provider": config.embedder_provider,
        "embedder_model": config.embedder_model,
        "embedder_dims": config.embedder_dims,
        "vector_store_provider": config.vector_store_provider,
        "vector_store_base_path": config.vector_store_base_path,
        
        # Configuración de planificador
        "planner_llm": str(config.planner_llm) if config.planner_llm else None,
        "use_vision_for_planner": config.use_vision_for_planner,
        "planner_interval": config.planner_interval,
        
        # Configuración inicial
        "initial_actions": config.initial_actions,
        "keep_alive": config.keep_alive,
        "storage_state": config.storage_state,
        
        # Configuración del modelo
        "model_provider": config.model_provider,
        "model_name": config.model_name,
        "temperature": config.temperature,
        
        # Configuración del viewport
        "viewport": config.viewport,
        "device_scale_factor": config.device_scale_factor,
        
        # Configuración de grabación
        "record_video_dir": config.record_video_dir,
        "trace_path": config.trace_path
    }


def _dict_to_browser_helper_config(config_dict: Dict[str, Any]) -> BrowserHelperConfig:
    """Convierte un diccionario a una instancia de BrowserHelperConfig."""
    # Filtrar valores None del diccionario, EXCEPTO user_data_dir que puede ser None explícitamente
    filtered_dict = {}
    for k, v in config_dict.items():
        if k == "user_data_dir":
            # Permitir que user_data_dir sea None explícitamente
            filtered_dict[k] = v
        elif v is not None:
            # Para otros campos, filtrar valores None
            filtered_dict[k] = v
    
    return BrowserHelperConfig(**filtered_dict)


def _load_custom_config(config_id: str) -> Optional[Dict[str, Any]]:
    """Carga una configuración personalizada desde el archivo."""
    config_file = CONFIG_DIR / f"{config_id}.json"
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None


def _save_custom_config(config_id: str, config_data: Dict[str, Any]) -> None:
    """Guarda una configuración personalizada en el archivo."""
    config_file = CONFIG_DIR / f"{config_id}.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)


def _list_custom_configs() -> List[Dict[str, Any]]:
    """Lista todas las configuraciones personalizadas (excluyendo predefinidas modificadas)."""
    configs = []
    for config_file in CONFIG_DIR.glob("*.json"):
        # Excluir archivos de configuraciones predefinidas modificadas
        if config_file.name.startswith("predefined_"):
            continue

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                # Aplicar defaults del .env a la configuración
                if 'settings' in config_data:
                    config_data['settings'] = _apply_env_defaults(config_data['settings'])
                configs.append(config_data)
        except Exception as e:
            logging.info(f"Error cargando configuración {config_file}: {e}")
    return configs


@router.get("/predefined", response_model=List[PredefinedConfigResponse], summary="Obtener configuraciones predefinidas")
async def get_predefined_configurations():
    """
    Obtiene todas las configuraciones predefinidas disponibles.
    Primero busca versiones modificadas en archivos, luego usa las por defecto.

    Returns:
        Lista de configuraciones predefinidas con sus detalles
    """
    predefined_configs = []

    # Configuraciones base con valores por defecto usando get_config_by_type
    config_types = ["test_case", "smoke", "exploration", "exploration_deep", "test_suite"]

    config_names = {
        "test_case": "Test Case",
        "smoke": "Smoke",
        "exploration": "Exploration",
        "exploration_deep": "Exploration Deep",
        "test_suite": "Test Suite"
    }

    config_descriptions = {
        "test_case": "Configuración para ejecución de casos de prueba individuales",
        "smoke": "Configuración para pruebas rápidas de funcionalidad básica - smoke testing",
        "exploration": "Configuración para exploración general de aplicaciones web",
        "exploration_deep": "Configuración para exploración exhaustiva y detallada de aplicaciones complejas",
        "test_suite": "Configuración para ejecución de suites completas con navegador persistente"
    }

    config_execution_types = {
        "test_case": ["case"],
        "smoke": ["smoke"],
        "exploration": ["full"],
        "exploration_deep": ["codegen"],
        "test_suite": ["suite"]
    }

    # Las configuraciones base se generan dinámicamente en el loop principal

    # Para cada tipo de configuración, verificar si existe una versión modificada
    logging.info(f"Processing config types: {config_types}")
    for config_type in config_types:
        # Siempre generar la configuración base completa
        try:
            config = get_config_by_type(config_type)
            config_dict = _browser_helper_config_to_dict(config)
            base_settings = _apply_env_defaults(config_dict)

            base_config = {
                "name": config_names.get(config_type, config_type.title()),
                "description": config_descriptions.get(config_type, f"Configuración {config_type}"),
                "settings": base_settings,
                "execution_types": config_execution_types.get(config_type, [])
            }

            logging.info(f"Generated base config for {config_type}: {len(base_settings)} fields")

        except Exception as e:
            logging.warning(f"Error loading base config for {config_type}: {e}")
            continue

        config_file = PREDEFINED_MODIFIED_DIR / f"{config_type}.json"

        if config_file.exists():
            # Cargar configuración modificada y hacer merge
            try:
                logging.info(f"Loading modified predefined config from: {config_file}")
                with open(config_file, 'r') as f:
                    modified_config = json.load(f)
                logging.info(f"Loaded modified config: {modified_config.get('name', 'Unknown')}")

                # Hacer merge de la configuración base con la modificada
                # Primero tomamos todos los settings de la configuración base
                merged_settings = base_config["settings"].copy()
                # Luego sobrescribimos con los valores modificados
                modified_settings = modified_config.get("settings", {})
                merged_settings.update(modified_settings)
                # Finalmente aplicamos defaults del entorno
                merged_settings = _apply_env_defaults(merged_settings)

                predefined_configs.append({
                    "config_type": config_type,
                    "name": modified_config.get("name", base_config["name"]),
                    "description": modified_config.get("description", base_config["description"]),
                    "settings": merged_settings,
                    "execution_types": modified_config.get("execution_types", base_config["execution_types"]),
                    "is_modified": True,
                    "updated_at": modified_config.get("updated_at")
                })
            except Exception as e:
                logging.warning(f"Error cargando configuración predefinida modificada {config_type}: {e}")
                # Usar configuración base si hay error
                predefined_configs.append({
                    "config_type": config_type,
                    **base_config,
                    "is_modified": False
                })
        else:
            # Usar configuración base
            predefined_configs.append({
                "config_type": config_type,
                **base_config,
                "is_modified": False
            })

    return predefined_configs


@router.get("/predefined/{config_type}", response_model=PredefinedConfigResponse, summary="Obtener configuración predefinida específica")
async def get_predefined_configuration(config_type: str):
    """
    Obtiene una configuración predefinida específica.
    
    Args:
        config_type: Tipo de configuración (ci, smoke, regression, dev, web, api, load, test_suite)
        
    Returns:
        Configuración predefinida solicitada
    """
    try:
        config = get_config_by_type(config_type)
        
        config_names = {
            "ci": "CI/CD",
            "smoke": "Smoke Test",
            "regression": "Regression Test",
            "dev": "Development",
            "web": "Web Interface",
            "api": "API Testing",
            "load": "Load Testing",
            "test_suite": "Test Suite"
        }
        
        config_descriptions = {
            "ci": "Configuración optimizada para CI/CD - velocidad máxima",
            "smoke": "Configuración para smoke tests - balance entre velocidad y confiabilidad",
            "regression": "Configuración para tests de regresión - máxima confiabilidad",
            "dev": "Configuración para desarrollo - debugging y observación",
            "web": "Configuración para interfaz web - balance para usuarios",
            "api": "Configuración para testing de APIs - enfoque en funcionalidad",
            "load": "Configuración para load testing - mínimo overhead",
            "test_suite": "Configuración especializada para ejecución de suites completas - evita límites de cuota API"
        }
        
        return PredefinedConfigResponse(
            config_type=config_type,
            name=config_names.get(config_type, config_type.title()),
            description=config_descriptions.get(config_type, f"Configuración {config_type}"),
            settings=_browser_helper_config_to_dict(config)
        )
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Configuración {config_type} no encontrada: {str(e)}")


@router.post("/validate", summary="Validar configuración")
async def validate_configuration(config_request: TestConfigurationRequest):
    """
    Valida una configuración de pruebas.
    
    Args:
        config_request: Configuración a validar
        
    Returns:
        Resultado de la validación con advertencias si las hay
    """
    try:
        # Convertir la request a diccionario y crear BrowserHelperConfig
        config_dict = config_request.dict(exclude_none=True)
        browser_config = _dict_to_browser_helper_config(config_dict)
        
        # Validar la configuración
        warnings = validate_config(browser_config)
        
        return {
            "valid": len(warnings) == 0,
            "warnings": warnings,
            "message": "Configuración válida" if len(warnings) == 0 else "Configuración con advertencias"
        }
    except Exception as e:
        return {
            "valid": False,
            "warnings": [],
            "message": f"Error en la configuración: {str(e)}"
        }


@router.get("/custom", response_model=List[TestConfigurationResponse], summary="Obtener configuraciones personalizadas")
async def get_custom_configurations():
    """
    Obtiene todas las configuraciones personalizadas guardadas.
    
    Returns:
        Lista de configuraciones personalizadas
    """
    configs = _list_custom_configs()
    return [TestConfigurationResponse(**config) for config in configs]


@router.post("/custom", response_model=TestConfigurationResponse, summary="Crear configuración personalizada")
async def create_custom_configuration(config_create: TestConfigurationCreateRequest):
    """
    Crea una nueva configuración personalizada.
    
    Args:
        config_create: Datos de la nueva configuración
        
    Returns:
        Configuración creada
    """
    try:
        # Generar ID único
        config_id = str(uuid.uuid4())
        
        # Validar la configuración
        config_dict = config_create.settings.dict(exclude_none=True)
        # Aplicar defaults del .env
        config_dict = _apply_env_defaults(config_dict)
        browser_config = _dict_to_browser_helper_config(config_dict)
        warnings = validate_config(browser_config)
        
        # Preparar datos de la configuración
        now = datetime.now().isoformat()
        config_data = {
            "config_id": config_id,
            "name": config_create.name,
            "description": config_create.description,
            "config_type": config_create.config_type,
            "settings": config_dict,
            "created_at": now,
            "updated_at": now,
            "is_default": config_create.is_default
        }
        
        # Si hay advertencias, incluirlas en la respuesta pero permitir guardar
        if warnings:
            config_data["validation_warnings"] = warnings
        
        # Guardar la configuración
        _save_custom_config(config_id, config_data)
        
        return TestConfigurationResponse(**config_data)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error creando configuración: {str(e)}")


@router.get("/custom/{config_id}", response_model=TestConfigurationResponse, summary="Obtener configuración personalizada")
async def get_custom_configuration(config_id: str):
    """
    Obtiene una configuración personalizada específica.
    
    Args:
        config_id: ID de la configuración
        
    Returns:
        Configuración solicitada
    """
    config_data = _load_custom_config(config_id)
    if not config_data:
        raise HTTPException(status_code=404, detail="Configuración no encontrada")
    
    # Aplicar defaults del .env
    if 'settings' in config_data:
        config_data['settings'] = _apply_env_defaults(config_data['settings'])
    
    return TestConfigurationResponse(**config_data)


@router.put("/predefined/{config_type}", response_model=PredefinedConfigResponse, summary="Actualizar configuración predefinida")
async def update_predefined_configuration(config_type: str, config_update: TestConfigurationUpdateRequest):
    """
    Actualiza una configuración predefinida.

    Args:
        config_type: Tipo de configuración predefinida
        config_update: Datos de actualización

    Returns:
        Configuración predefinida actualizada
    """
    try:
        # Validar que el tipo de configuración existe
        valid_types = ["test_case", "smoke", "exploration", "exploration_deep", "test_suite"]
        if config_type not in valid_types:
            raise HTTPException(status_code=404, detail=f"Tipo de configuración {config_type} no encontrado")

        # Validar la configuración actualizada
        config_dict = config_update.settings.dict(exclude_none=True) if config_update.settings else {}
        config_dict = _apply_env_defaults(config_dict)
        browser_config = _dict_to_browser_helper_config(config_dict)
        warnings = validate_config(browser_config)

        # Guardar la configuración predefinida actualizada en un archivo JSON
        config_file = PREDEFINED_MODIFIED_DIR / f"{config_type}.json"
        config_data = {
            "config_type": config_type,
            "name": config_update.name or f"Updated {config_type.title()}",
            "description": config_update.description or f"Updated {config_type} configuration",
            "settings": config_dict,
            "execution_types": getattr(config_update, 'execution_types', []) if hasattr(config_update, 'execution_types') else [],
            "updated_at": datetime.now().isoformat(),
            "is_modified": True,
            "warnings": warnings
        }

        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)

        # Preparar respuesta
        config_names = {
            "test_case": "Test Case / Test Suite",
            "smoke": "Smoke",
            "exploration": "Exploration",
            "exploration_deep": "Exploration Deep"
        }

        return PredefinedConfigResponse(
            config_type=config_type,
            name=config_data["name"],
            description=config_data["description"],
            settings=config_dict
        )

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error actualizando configuración predefinida: {str(e)}")


@router.put("/custom/{config_id}", response_model=TestConfigurationResponse, summary="Actualizar configuración personalizada")
async def update_custom_configuration(config_id: str, config_update: TestConfigurationUpdateRequest):
    """
    Actualiza una configuración personalizada existente.
    
    Args:
        config_id: ID de la configuración
        config_update: Datos actualizados
        
    Returns:
        Configuración actualizada
    """
    # Cargar configuración existente
    config_data = _load_custom_config(config_id)
    if not config_data:
        raise HTTPException(status_code=404, detail="Configuración no encontrada")
    
    try:
        # Actualizar solo los campos proporcionados
        update_dict = config_update.dict(exclude_none=True)
        
        if "name" in update_dict:
            config_data["name"] = update_dict["name"]
        if "description" in update_dict:
            config_data["description"] = update_dict["description"]
        if "config_type" in update_dict:
            config_data["config_type"] = update_dict["config_type"]
        if "is_default" in update_dict:
            config_data["is_default"] = update_dict["is_default"]
        if "settings" in update_dict:
            # Validar nueva configuración
            new_settings = update_dict["settings"]
            # Aplicar defaults del .env
            new_settings = _apply_env_defaults(new_settings)
            browser_config = _dict_to_browser_helper_config(new_settings)
            warnings = validate_config(browser_config)
            
            config_data["settings"] = new_settings
            if warnings:
                config_data["validation_warnings"] = warnings
            else:
                config_data.pop("validation_warnings", None)
        
        config_data["updated_at"] = datetime.now().isoformat()
        
        # Guardar configuración actualizada
        _save_custom_config(config_id, config_data)
        
        return TestConfigurationResponse(**config_data)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error actualizando configuración: {str(e)}")


@router.delete("/custom/{config_id}", summary="Eliminar configuración personalizada")
async def delete_custom_configuration(config_id: str):
    """
    Elimina una configuración personalizada.
    
    Args:
        config_id: ID de la configuración
        
    Returns:
        Confirmación de eliminación
    """
    config_file = CONFIG_DIR / f"{config_id}.json"
    if not config_file.exists():
        raise HTTPException(status_code=404, detail="Configuración no encontrada")
    
    try:
        config_file.unlink()
        return {"message": "Configuración eliminada exitosamente", "config_id": config_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error eliminando configuración: {str(e)}")


@router.post("/validate-execution-types", summary="Validar conflictos de tipos de ejecución")
async def validate_execution_types(request: dict):
    """
    Valida si hay conflictos entre tipos de ejecución de configuraciones.

    Args:
        request: Diccionario con config_id, execution_types y exclude_config_id (opcional)

    Returns:
        Información sobre conflictos encontrados
    """
    try:
        config_id = request.get("config_id")
        execution_types = request.get("execution_types", [])
        exclude_config_id = request.get("exclude_config_id")  # Para excluir la configuración actual al editar

        conflicts = []

        # Obtener todas las configuraciones
        predefined = await get_predefined_configurations()
        custom = await get_custom_configurations()
        all_configs = predefined + [config.dict() for config in custom]

        # Buscar conflictos
        for config in all_configs:
            # Excluir la configuración actual si se está editando
            if exclude_config_id and config.get("config_id") == exclude_config_id:
                continue
            if exclude_config_id and config.get("config_type") == exclude_config_id:
                continue

            config_execution_types = config.get("execution_types", [])

            # Encontrar tipos de ejecución en común
            common_types = set(execution_types) & set(config_execution_types)

            if common_types:
                conflicts.append({
                    "config_id": config.get("config_id", config.get("config_type")),
                    "config_name": config.get("name"),
                    "config_type": config.get("config_type", "custom"),
                    "conflicting_execution_types": list(common_types),
                    "is_predefined": "config_type" in config and "config_id" not in config
                })

        return {
            "has_conflicts": len(conflicts) > 0,
            "conflicts": conflicts,
            "suggested_action": "remove_from_existing" if conflicts else "proceed"
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error validando tipos de ejecución: {str(e)}")


@router.post("/resolve-execution-type-conflict", summary="Resolver conflicto de tipos de ejecución")
async def resolve_execution_type_conflict(request: dict):
    """
    Resuelve conflictos de tipos de ejecución removiendo tipos de configuraciones existentes.

    Args:
        request: Diccionario con action, target_config, source_config, execution_types

    Returns:
        Resultado de la resolución del conflicto
    """
    try:
        action = request.get("action")  # "remove_from_existing" o "cancel"
        target_config = request.get("target_config")  # Configuración que recibirá los tipos
        conflicts = request.get("conflicts", [])
        execution_types_to_move = request.get("execution_types", [])

        if action == "cancel":
            return {"success": True, "message": "Operación cancelada"}

        if action == "remove_from_existing":
            updated_configs = []

            for conflict in conflicts:
                config_id = conflict["config_id"]
                is_predefined = conflict["is_predefined"]
                conflicting_types = conflict["conflicting_execution_types"]

                if is_predefined:
                    # Actualizar configuración predefinida
                    config_file = CONFIG_DIR / f"predefined_{config_id}.json"
                    if config_file.exists():
                        with open(config_file, 'r') as f:
                            config_data = json.load(f)

                        # Remover tipos de ejecución conflictivos
                        current_types = config_data.get("execution_types", [])
                        new_types = [t for t in current_types if t not in conflicting_types]
                        config_data["execution_types"] = new_types
                        config_data["updated_at"] = datetime.now().isoformat()

                        with open(config_file, 'w') as f:
                            json.dump(config_data, f, indent=2)

                        updated_configs.append({
                            "config_id": config_id,
                            "name": config_data["name"],
                            "removed_types": conflicting_types
                        })
                else:
                    # Actualizar configuración personalizada
                    config_file = CONFIG_DIR / f"{config_id}.json"
                    if config_file.exists():
                        with open(config_file, 'r') as f:
                            config_data = json.load(f)

                        # Remover tipos de ejecución conflictivos
                        current_types = config_data.get("execution_types", [])
                        new_types = [t for t in current_types if t not in conflicting_types]
                        config_data["execution_types"] = new_types
                        config_data["updated_at"] = datetime.now().isoformat()

                        with open(config_file, 'w') as f:
                            json.dump(config_data, f, indent=2)

                        updated_configs.append({
                            "config_id": config_id,
                            "name": config_data["name"],
                            "removed_types": conflicting_types
                        })

            return {
                "success": True,
                "message": f"Conflictos resueltos. {len(updated_configs)} configuraciones actualizadas.",
                "updated_configs": updated_configs
            }

        return {"success": False, "message": "Acción no reconocida"}

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error resolviendo conflicto: {str(e)}")


@router.post("/test", summary="Probar configuración")
async def test_configuration(config_request: TestConfigurationRequest):
    """
    Prueba una configuración creando una instancia de BrowserHelperConfig y validándola.
    
    Args:
        config_request: Configuración a probar
        
    Returns:
        Resultado de la prueba con detalles de la configuración aplicada
    """
    try:
        # Convertir la request a diccionario y crear BrowserHelperConfig
        config_dict = config_request.dict(exclude_none=True)
        browser_config = _dict_to_browser_helper_config(config_dict)
        
        # Validar la configuración
        warnings = validate_config(browser_config)
        
        # Obtener la configuración final aplicada
        final_config = _browser_helper_config_to_dict(browser_config)
        
        return {
            "success": True,
            "warnings": warnings,
            "final_config": final_config,
            "message": "Configuración probada exitosamente"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Error probando configuración"
        }


@router.get("/", summary="Obtener todas las configuraciones")
async def get_all_configurations():
    """
    Obtiene todas las configuraciones disponibles (predefinidas y personalizadas).
    
    Returns:
        Diccionario con configuraciones predefinidas y personalizadas
    """
    # Obtener configuraciones predefinidas
    predefined = await get_predefined_configurations()
    
    # Obtener configuraciones personalizadas (filtrar archivos predefinidos)
    custom_configs_raw = _list_custom_configs()  # Usar la función directa para evitar validación
    custom = []
    for config in custom_configs_raw:
        try:
            # Validar que tiene los campos requeridos para TestConfigurationResponse
            if 'config_id' in config and 'created_at' in config:
                custom.append(TestConfigurationResponse(**config).dict())
        except Exception as e:
            logging.warning(f"Error validando configuración personalizada: {e}")
            continue

    return {
        "predefined": predefined,
        "custom": custom
    }


@router.get("/defaults", summary="Obtener valores por defecto del entorno")
async def get_environment_defaults():
    """
    Obtiene los valores por defecto desde las variables de entorno.
    Útil para inicializar formularios con valores predeterminados.
    
    Returns:
        Diccionario con valores por defecto del .env
    """
    return _get_env_defaults()


# ============================================================================
# NUEVOS ENDPOINTS PARA MONGODB Y SESIONES DE BROWSER
# ============================================================================

@router.post("/mongodb/configurations", summary="Crear configuración en MongoDB")
async def create_mongodb_configuration(
    config_create: TestConfigurationCreateRequest,
    project_id: Optional[str] = None,
    suite_id: Optional[str] = None,
    created_by: Optional[str] = None,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Crea una nueva configuración de browser en MongoDB.
    
    Args:
        config_create: Datos de la nueva configuración
        project_id: ID del proyecto (opcional)
        suite_id: ID de la suite (opcional)
        created_by: Usuario que crea la configuración (opcional)
        
    Returns:
        Configuración creada con warnings de validación
    """
    try:
        # Aplicar defaults del entorno
        settings = _apply_env_defaults(config_create.settings)
        
        config, warnings = await config_service.create_configuration(
            name=config_create.name,
            settings=settings,
            description=config_create.description,
            config_type=getattr(config_create, 'config_type', 'custom'),
            created_by=created_by,
            project_id=project_id,
            suite_id=suite_id,
            tags=getattr(config_create, 'tags', []),
            execution_types=getattr(config_create, 'execution_types', None),
            is_default=getattr(config_create, 'is_default', False)
        )
        
        return {
            "config_id": config.config_id,
            "name": config.name,
            "description": config.description,
            "config_type": config.config_type,
            "settings": config.settings,
            "execution_types": [et.value for et in config.execution_types],
            "validation_warnings": warnings,
            "is_valid": config.is_valid,
            "created_at": config.created_at.isoformat(),
            "usage_count": config.usage_count
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logging.error(f"Error creating MongoDB configuration: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/mongodb/configurations", summary="Listar configuraciones de MongoDB")
async def list_mongodb_configurations(
    project_id: Optional[str] = None,
    suite_id: Optional[str] = None,
    config_type: Optional[str] = None,
    include_global: bool = True,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Lista configuraciones de browser desde MongoDB con filtros opcionales.
    
    Args:
        project_id: Filtrar por proyecto
        suite_id: Filtrar por suite
        config_type: Filtrar por tipo de configuración
        include_global: Incluir configuraciones globales
        
    Returns:
        Lista de configuraciones
    """
    try:
        configs = await config_service.list_configurations(
            project_id=project_id,
            suite_id=suite_id,
            config_type=config_type,
            include_global=include_global
        )
        
        return [
            {
                "config_id": config.config_id,
                "name": config.name,
                "description": config.description,
                "config_type": config.config_type,
                "settings": config.settings,
                "execution_types": [et.value for et in config.execution_types],
                "is_valid": config.is_valid,
                "validation_warnings": config.validation_warnings,
                "created_at": config.created_at.isoformat(),
                "updated_at": config.updated_at.isoformat() if config.updated_at else None,
                "usage_count": config.usage_count,
                "is_default": config.is_default,
                "project_id": config.project_id,
                "suite_id": config.suite_id,
                "tags": config.tags
            }
            for config in configs
        ]
        
    except Exception as e:
        logging.error(f"Error listing MongoDB configurations: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/mongodb/configurations/execution-type/{execution_type}", summary="Obtener configuraciones por tipo de ejecución")
async def get_configurations_by_execution_type(
    execution_type: str,
    project_id: Optional[str] = None,
    suite_id: Optional[str] = None,
    include_inactive: bool = False,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Obtiene configuraciones que soportan un tipo específico de ejecución.
    
    Args:
        execution_type: Tipo de ejecución (smoke, full, case, suite, codegen)
        project_id: Filtrar por proyecto
        suite_id: Filtrar por suite
        include_inactive: Incluir configuraciones inactivas
        
    Returns:
        Lista de configuraciones compatibles
    """
    try:
        # Validar tipo de ejecución
        valid_types = ["smoke", "full", "case", "suite", "codegen"]
        if execution_type.lower() not in valid_types:
            raise HTTPException(
                status_code=400, 
                detail=f"Tipo de ejecución inválido. Debe ser uno de: {', '.join(valid_types)}"
            )
        
        configs = await config_service.get_configurations_by_execution_type(
            execution_type=execution_type.lower(),
            project_id=project_id,
            suite_id=suite_id,
            include_inactive=include_inactive
        )
        
        return [
            {
                "config_id": config.config_id,
                "name": config.name,
                "description": config.description,
                "config_type": config.config_type,
                "settings": config.settings,
                "execution_types": [et.value for et in config.execution_types],
                "is_valid": config.is_valid,
                "validation_warnings": config.validation_warnings,
                "created_at": config.created_at.isoformat(),
                "updated_at": config.updated_at.isoformat() if config.updated_at else None,
                "usage_count": config.usage_count,
                "is_default": config.is_default,
                "project_id": config.project_id,
                "suite_id": config.suite_id,
                "tags": config.tags
            }
            for config in configs
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting configurations by execution type: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/mongodb/configurations/{config_id}", summary="Obtener configuración de MongoDB")
async def get_mongodb_configuration(
    config_id: str,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Obtiene una configuración específica desde MongoDB.
    
    Args:
        config_id: ID de la configuración
        
    Returns:
        Configuración solicitada
    """
    try:
        config = await config_service.get_configuration(config_id)
        if not config:
            raise HTTPException(status_code=404, detail="Configuración no encontrada")
        
        # Incrementar contador de uso
        await config_service.increment_usage(config_id)
        
        return {
            "config_id": config.config_id,
            "name": config.name,
            "description": config.description,
            "config_type": config.config_type,
            "settings": config.settings,
            "execution_types": [et.value for et in config.execution_types],
            "is_valid": config.is_valid,
            "validation_warnings": config.validation_warnings,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "usage_count": config.usage_count,
            "is_default": config.is_default,
            "project_id": config.project_id,
            "suite_id": config.suite_id,
            "tags": config.tags
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting MongoDB configuration: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.put("/mongodb/configurations/{config_id}", summary="Actualizar configuración de MongoDB")
async def update_mongodb_configuration(
    config_id: str,
    config_update: TestConfigurationUpdateRequest,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Actualiza una configuración existente en MongoDB.
    
    Args:
        config_id: ID de la configuración
        config_update: Datos actualizados
        
    Returns:
        Configuración actualizada
    """
    try:
        # Aplicar defaults del entorno si hay nuevos settings
        settings = None
        if config_update.settings is not None:
            settings = _apply_env_defaults(config_update.settings)
        
        config, warnings = await config_service.update_configuration(
            config_id=config_id,
            name=config_update.name,
            settings=settings,
            description=config_update.description,
            tags=getattr(config_update, 'tags', None),
            execution_types=getattr(config_update, 'execution_types', None),
            is_default=getattr(config_update, 'is_default', None)
        )
        
        return {
            "config_id": config.config_id,
            "name": config.name,
            "description": config.description,
            "config_type": config.config_type,
            "settings": config.settings,
            "execution_types": [et.value for et in config.execution_types],
            "validation_warnings": warnings,
            "is_valid": config.is_valid,
            "updated_at": config.updated_at.isoformat(),
            "usage_count": config.usage_count
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logging.error(f"Error updating MongoDB configuration: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.patch("/mongodb/configurations/{config_id}/execution-types", summary="Actualizar tipos de ejecución de configuración")
async def update_configuration_execution_types(
    config_id: str,
    execution_types: List[str],
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Actualiza los tipos de ejecución que pueden usar una configuración.
    
    Args:
        config_id: ID de la configuración
        execution_types: Lista de tipos de ejecución (smoke, full, case, suite, codegen)
        
    Returns:
        Configuración actualizada
    """
    try:
        # Validar tipos de ejecución
        valid_types = ["smoke", "full", "case", "suite", "codegen"]
        invalid_types = [et for et in execution_types if et.lower() not in valid_types]
        
        if invalid_types:
            raise HTTPException(
                status_code=400,
                detail=f"Tipos de ejecución inválidos: {', '.join(invalid_types)}. Debe ser uno de: {', '.join(valid_types)}"
            )
        
        if not execution_types:
            raise HTTPException(
                status_code=400,
                detail="Debe especificar al menos un tipo de ejecución"
            )
        
        # Normalizar a minúsculas
        normalized_types = [et.lower() for et in execution_types]
        
        config = await config_service.update_configuration_execution_types(
            config_id=config_id,
            execution_types=normalized_types
        )
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuración no encontrada")
        
        return {
            "config_id": config.config_id,
            "name": config.name,
            "description": config.description,
            "config_type": config.config_type,
            "execution_types": [et.value for et in config.execution_types],
            "updated_at": config.updated_at.isoformat(),
            "message": f"Tipos de ejecución actualizados: {', '.join(normalized_types)}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error updating configuration execution types: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.delete("/mongodb/configurations/{config_id}", summary="Eliminar configuración de MongoDB")
async def delete_mongodb_configuration(
    config_id: str,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Elimina (desactiva) una configuración de MongoDB.
    
    Args:
        config_id: ID de la configuración
        
    Returns:
        Confirmación de eliminación
    """
    try:
        success = await config_service.delete_configuration(config_id)
        if not success:
            raise HTTPException(status_code=404, detail="Configuración no encontrada")
        
        return {"message": "Configuración eliminada exitosamente"}
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error deleting MongoDB configuration: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.post("/mongodb/configurations/{config_id}/clone", summary="Clonar configuración de MongoDB")
async def clone_mongodb_configuration(
    config_id: str,
    new_name: str,
    created_by: Optional[str] = None,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Clona una configuración existente en MongoDB.
    
    Args:
        config_id: ID de la configuración a clonar
        new_name: Nombre para la nueva configuración
        created_by: Usuario que clona la configuración
        
    Returns:
        Nueva configuración clonada
    """
    try:
        cloned_config = await config_service.clone_configuration(
            config_id=config_id,
            new_name=new_name,
            created_by=created_by
        )
        
        if not cloned_config:
            raise HTTPException(status_code=404, detail="Configuración original no encontrada")
        
        return {
            "config_id": cloned_config.config_id,
            "name": cloned_config.name,
            "description": cloned_config.description,
            "config_type": cloned_config.config_type,
            "settings": cloned_config.settings,
            "created_at": cloned_config.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error cloning MongoDB configuration: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


# ============================================================================
# ENDPOINTS PARA GESTIÓN DE SESIONES DE BROWSER PARA TEST SUITES
# ============================================================================

@router.post("/sessions/suite/{suite_id}", summary="Crear/Obtener sesión para test suite")
async def get_or_create_suite_session(
    suite_id: str,
    project_id: str,
    config_id: str,
    browser_type: str = "chromium",
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Obtiene una sesión existente o indica que se necesita crear una nueva para una test suite.
    
    Args:
        suite_id: ID de la test suite
        project_id: ID del proyecto
        config_id: ID de la configuración a usar
        browser_type: Tipo de browser (chromium, firefox, webkit)
        
    Returns:
        Información de la sesión o indicación de crear nueva
    """
    try:
        session = await config_service.get_or_create_session_for_suite(
            suite_id=suite_id,
            project_id=project_id,
            config_id=config_id,
            browser_type=browser_type
        )
        
        if session:
            return {
                "session_found": True,
                "session_id": session.session_id,
                "pool_id": session.pool_id,
                "browser_type": session.browser_type,
                "configuration": session.configuration,
                "status": session.status,
                "cdp_url": session.cdp_url,
                "ws_endpoint": session.ws_endpoint,
                "expires_at": session.expires_at.isoformat() if session.expires_at else None
            }
        else:
            return {
                "session_found": False,
                "message": "No hay sesión disponible, se debe crear una nueva",
                "recommended_config": await config_service.get_configuration(config_id)
            }
            
    except Exception as e:
        logging.error(f"Error getting/creating suite session: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.post("/sessions/register", summary="Registrar nueva sesión de browser")
async def register_browser_session(
    suite_id: str,
    project_id: str,
    config_id: str,
    session_id: str,
    browser_type: str = "chromium",
    user_data_dir: Optional[str] = None,
    cdp_url: Optional[str] = None,
    ws_endpoint: Optional[str] = None,
    expires_in_hours: int = 24,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Registra una nueva sesión de browser en el pool para una test suite.
    
    Args:
        suite_id: ID de la test suite
        project_id: ID del proyecto
        config_id: ID de la configuración
        session_id: ID único de la sesión de browser
        browser_type: Tipo de browser
        user_data_dir: Directorio de datos del usuario
        cdp_url: URL del Chrome DevTools Protocol
        ws_endpoint: WebSocket endpoint
        expires_in_hours: Horas hasta expiración
        
    Returns:
        Información de la sesión registrada
    """
    try:
        session_pool = await config_service.create_session_pool(
            suite_id=suite_id,
            project_id=project_id,
            config_id=config_id,
            session_id=session_id,
            browser_type=browser_type,
            user_data_dir=user_data_dir,
            cdp_url=cdp_url,
            ws_endpoint=ws_endpoint,
            expires_in_hours=expires_in_hours
        )
        
        return {
            "pool_id": session_pool.pool_id,
            "session_id": session_pool.session_id,
            "suite_id": session_pool.suite_id,
            "status": session_pool.status,
            "expires_at": session_pool.expires_at.isoformat(),
            "message": "Sesión registrada exitosamente"
        }
        
    except Exception as e:
        logging.error(f"Error registering browser session: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.post("/sessions/{suite_id}/lock", summary="Bloquear sesión para test")
async def lock_session_for_test(
    suite_id: str,
    test_case_id: str,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Bloquea una sesión disponible para la ejecución de un test específico.
    
    Args:
        suite_id: ID de la test suite
        test_case_id: ID del test case
        
    Returns:
        Información de la sesión bloqueada
    """
    try:
        session = await config_service.lock_session_for_test(suite_id, test_case_id)
        
        if not session:
            raise HTTPException(
                status_code=404, 
                detail="No hay sesiones disponibles para bloquear"
            )
        
        return {
            "session_id": session.session_id,
            "pool_id": session.pool_id,
            "locked_for_test": test_case_id,
            "cdp_url": session.cdp_url,
            "ws_endpoint": session.ws_endpoint,
            "configuration": session.configuration
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error locking session for test: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.post("/sessions/{session_id}/unlock", summary="Desbloquear sesión")
async def unlock_session(
    session_id: str,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Desbloquea una sesión después de completar un test.
    
    Args:
        session_id: ID de la sesión
        
    Returns:
        Confirmación de desbloqueo
    """
    try:
        success = await config_service.unlock_session(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Sesión no encontrada")
        
        return {"message": "Sesión desbloqueada exitosamente"}
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error unlocking session: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.put("/sessions/{session_id}/health", summary="Actualizar estado de salud de sesión")
async def update_session_health(
    session_id: str,
    status: str,
    error: Optional[str] = None,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Actualiza el estado de salud de una sesión.
    
    Args:
        session_id: ID de la sesión
        status: Estado (healthy, unhealthy, error)
        error: Mensaje de error (opcional)
        
    Returns:
        Confirmación de actualización
    """
    try:
        success = await config_service.update_session_health(session_id, status, error)
        
        if not success:
            raise HTTPException(status_code=404, detail="Sesión no encontrada")
        
        return {"message": "Estado de salud actualizado exitosamente"}
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error updating session health: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/sessions/suite/{suite_id}", summary="Listar sesiones de test suite")
async def list_suite_sessions(
    suite_id: str,
    active_only: bool = True,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Lista todas las sesiones de una test suite.
    
    Args:
        suite_id: ID de la test suite
        active_only: Solo sesiones activas
        
    Returns:
        Lista de sesiones
    """
    try:
        sessions = await config_service.list_suite_sessions(suite_id, active_only)
        
        return [
            {
                "pool_id": session.pool_id,
                "session_id": session.session_id,
                "status": session.status,
                "browser_type": session.browser_type,
                "locked_by_test": session.locked_by_test,
                "health_status": session.health_status,
                "last_health_check": session.last_health_check.isoformat() if session.last_health_check else None,
                "created_at": session.created_at.isoformat(),
                "expires_at": session.expires_at.isoformat() if session.expires_at else None
            }
            for session in sessions
        ]
        
    except Exception as e:
        logging.error(f"Error listing suite sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/sessions/suite/{suite_id}/stats", summary="Estadísticas de sesiones")
async def get_session_stats(
    suite_id: str,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Obtiene estadísticas de sesiones para una test suite.
    
    Args:
        suite_id: ID de la test suite
        
    Returns:
        Estadísticas de sesiones
    """
    try:
        stats = await config_service.get_session_stats(suite_id)
        return stats
        
    except Exception as e:
        logging.error(f"Error getting session stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.post("/sessions/cleanup", summary="Limpiar sesiones expiradas")
async def cleanup_sessions(
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Limpia sesiones expiradas y no saludables.
    
    Returns:
        Estadísticas de limpieza
    """
    try:
        cleanup_stats = await config_service.cleanup_sessions()
        return cleanup_stats
        
    except Exception as e:
        logging.error(f"Error cleaning up sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


# ============================================================================
# ENDPOINTS PARA MIGRACIÓN DE CONFIGURACIONES LEGACY
# ============================================================================

@router.post("/migrate/legacy", summary="Migrar configuraciones legacy a MongoDB")
async def migrate_legacy_configurations(
    backup_existing: bool = True,
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Migra configuraciones legacy desde archivos JSON a MongoDB.
    
    Args:
        backup_existing: Crear backup de configuraciones existentes
        
    Returns:
        Reporte de migración
    """
    try:
        from pathlib import Path
        
        legacy_config_dir = Path("config/custom")
        
        if backup_existing and legacy_config_dir.exists():
            # Crear backup
            import shutil
            from datetime import datetime
            backup_dir = Path(f"config/backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            shutil.copytree(legacy_config_dir, backup_dir)
            logging.info(f"Backup creado en: {backup_dir}")
        
        # Ejecutar migración
        migrated_count = await config_service.migrate_legacy_configurations(legacy_config_dir)
        
        return {
            "migrated_count": migrated_count,
            "backup_created": backup_existing,
            "message": f"Se migraron {migrated_count} configuraciones exitosamente"
        }
        
    except Exception as e:
        logging.error(f"Error during legacy migration: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/mongodb/predefined", summary="Obtener configuraciones predefinidas desde MongoDB")
async def get_mongodb_predefined_configurations(
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Obtiene las configuraciones predefinidas optimizadas para MongoDB.
    
    Returns:
        Lista de configuraciones predefinidas
    """
    try:
        predefined_configs = await config_service.get_predefined_configurations()
        return predefined_configs
        
    except Exception as e:
        logging.error(f"Error getting predefined configurations: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")


@router.get("/health", summary="Estado de salud del sistema de configuraciones")
async def configuration_health_check(
    config_service: BrowserConfigurationService = Depends(get_browser_config_service)
):
    """
    Verifica el estado de salud del sistema de configuraciones.
    
    Returns:
        Estado de salud y estadísticas
    """
    try:
        # Verificar conexión a MongoDB
        from src.database.models.browser_configuration import BrowserConfiguration
        
        # Contar configuraciones
        total_configs = await BrowserConfiguration.count()
        active_configs = await BrowserConfiguration.find({"is_active": True}).count()
        
        # Estadísticas básicas
        health_info = {
            "status": "healthy",
            "mongodb_connected": True,
            "total_configurations": total_configs,
            "active_configurations": active_configs,
            "predefined_configurations": 4,  # test_case, smoke, exploration, exploration_deep
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return health_info
        
    except Exception as e:
        logging.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "mongodb_connected": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# ============================================================================
# DEFAULT CONFIGURATION SYSTEM ENDPOINTS
# ============================================================================

@router.post("/defaults/seed", summary="Sembrar configuraciones predefinidas")
async def seed_predefined_configurations(
    default_service: DefaultConfigurationService = Depends(get_default_config_service)
):
    """
    Siembra las 4 configuraciones predefinidas como configuraciones modificables en MongoDB.
    Solo crea las que no existen.
    
    Returns:
        Resultado del proceso de siembra
    """
    return await default_service.seed_predefined_configurations()


@router.post("/defaults/initialize", summary="Inicializar sistema de configuraciones")
async def initialize_system_defaults(
    default_service: DefaultConfigurationService = Depends(get_default_config_service)
):
    """
    Inicializa el sistema completo de configuraciones por defecto.
    
    Returns:
        Resultado de la inicialización
    """
    return await default_service.initialize_system_defaults()


@router.get("/defaults/area/{area}", summary="Obtener configuración por defecto para área")
async def get_default_configuration_for_area(
    area: str,
    project_id: Optional[str] = Query(None, description="ID del proyecto"),
    default_service: DefaultConfigurationService = Depends(get_default_config_service)
):
    """
    Obtiene la configuración por defecto para un área específica.
    
    Args:
        area: Área (testing, smoke, exploration, etc.)
        project_id: ID del proyecto (opcional)
        
    Returns:
        Configuración por defecto para el área
    """
    config = await default_service.get_default_configuration_for_area(area, project_id)
    if not config:
        raise HTTPException(status_code=404, detail=f"No se encontró configuración por defecto para el área {area}")
    
    return config.dict()


@router.post("/defaults/project/{project_id}/area/{area}/set/{config_id}", summary="Establecer configuración por defecto para proyecto")
async def set_project_default_configuration(
    project_id: str,
    area: str,
    config_id: str,
    default_service: DefaultConfigurationService = Depends(get_default_config_service)
):
    """
    Establece una configuración como por defecto para un área específica en un proyecto.
    
    Args:
        project_id: ID del proyecto
        area: Área (testing, smoke, exploration, etc.)
        config_id: ID de la configuración a establecer como default
        
    Returns:
        Resultado de la operación
    """
    result = await default_service.set_project_default_configuration(project_id, area, config_id)
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    
    return result


@router.get("/defaults/area/{area}/configurations", summary="Obtener configuraciones para área")
async def get_area_configurations(
    area: str,
    project_id: Optional[str] = Query(None, description="ID del proyecto"),
    default_service: DefaultConfigurationService = Depends(get_default_config_service)
):
    """
    Obtiene todas las configuraciones disponibles para un área específica.
    
    Args:
        area: Área (testing, smoke, exploration, etc.)
        project_id: ID del proyecto (opcional)
        
    Returns:
        Lista de configuraciones para el área
    """
    configs = await default_service.get_area_configurations(area, project_id)
    return [config.dict() for config in configs]


@router.get("/defaults/areas", summary="Obtener todas las áreas disponibles")
async def get_available_areas():
    """
    Obtiene todas las áreas disponibles en el sistema.
    
    Returns:
        Lista de áreas disponibles
    """
    return {
        "areas": [
            {
                "key": "testing",
                "name": "Testing / Test Suites",
                "description": "Configuraciones para ejecución de casos de prueba y suites de testing"
            },
            {
                "key": "smoke",
                "name": "Smoke Testing",
                "description": "Configuraciones para pruebas rápidas de funcionalidad básica"
            },
            {
                "key": "exploration",
                "name": "Exploration",
                "description": "Configuraciones para exploración general y detallada de aplicaciones"
            }
        ]
    }
