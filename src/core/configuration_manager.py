"""
Configuration Manager for QAK Test Execution System

Simplifies browser configurations from 9 predefined types to 3 base profiles
with intelligent override system for environment-specific adaptations.
"""

import json
from typing import Dict, Any, Optional, Union
from enum import Enum
import os
from dataclasses import dataclass, field, fields
from pathlib import Path

from .cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, cache_manager


class ConfigProfile(str, Enum):
    """Base configuration profiles for test execution."""
    FAST = "fast"       # Maximum speed, minimal overhead
    BALANCED = "balanced"  # Balance between speed and reliability  
    THOROUGH = "thorough"  # Maximum reliability and comprehensive testing


class Environment(str, Enum):
    """Execution environments with specific requirements."""
    CI = "ci"
    DEVELOPMENT = "development" 
    PRODUCTION = "production"
    WEB = "web"
    API = "api"
    LOAD = "load"
    CAPTCHA = "captcha"


@dataclass
class BrowserConfig:
    """Unified browser configuration with validation."""
    # Core settings
    headless: bool = True
    use_vision: bool = True
    enable_memory: bool = False  # DEPRECATED: Memory removed in browser-use v0.5.0
    
    # Performance settings
    max_steps: int = 50
    wait_between_actions: float = 1.0
    minimum_wait_page_load_time: float = 0.5
    retry_delay: int = 10
    max_failures: int = 3
    
    # AI settings
    temperature: float = 0.1
    
    # Security and domain settings
    allowed_domains: Optional[list] = None
    disable_security: bool = True
    stealth: bool = False
    
    # Output and debugging
    generate_gif: bool = False
    highlight_elements: bool = False
    save_conversation_path: Optional[str] = None
    
    # Advanced features
    keep_alive: bool = False
    user_agent: Optional[str] = None
    
    # Environment-specific overrides
    overrides: Dict[str, Any] = field(default_factory=dict)

    def apply_overrides(self, overrides: Dict[str, Any]) -> 'BrowserConfig':
        """Apply configuration overrides and return new config."""
        config_dict = self.__dict__.copy()
        
        # Filter out keys that are not part of the dataclass definition
        valid_keys = {f.name for f in fields(self.__class__)}
        filtered_overrides = {k: v for k, v in overrides.items() if k in valid_keys}
        
        config_dict.update(filtered_overrides)
        # Remove 'overrides' from the dict to avoid recursion
        config_dict.pop('overrides', None)
        return BrowserConfig(**config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the configuration to a dictionary."""
        return self.__dict__.copy()


class ConfigurationManager:
    """
    Unified configuration manager that simplifies browser configurations
    from 9 predefined types to 3 base profiles with override system.
    """
    
    # Base configuration profiles
    BASE_CONFIGS = {
        ConfigProfile.FAST: BrowserConfig(
            headless=True,
            use_vision=False,
            enable_memory=False,  # Memory removed in browser-use v0.5.0
            max_steps=25,
            wait_between_actions=0.1,
            minimum_wait_page_load_time=0.05,
            temperature=0.0,
            max_failures=1,
            generate_gif=False
        ),
        
        ConfigProfile.BALANCED: BrowserConfig(
            headless=True,
            use_vision=True,
            enable_memory=False,  # Memory removed in browser-use v0.5.0
            max_steps=50,
            wait_between_actions=1.0,
            minimum_wait_page_load_time=0.5,
            temperature=0.1,
            max_failures=2,
            generate_gif=False
        ),
        
        ConfigProfile.THOROUGH: BrowserConfig(
            headless=True,
            use_vision=True,
            enable_memory=False,  # Memory removed in browser-use v0.5.0
            max_steps=200,
            wait_between_actions=2.0,
            minimum_wait_page_load_time=1.0,
            temperature=0.0,
            max_failures=3,
            generate_gif=True
        )
    }
    
    # Environment-specific overrides
    ENVIRONMENT_OVERRIDES = {
        Environment.CI: {
            "headless": True,
            "use_vision": False,
            "enable_memory": False,
            "max_steps": 25,
            "wait_between_actions": 0.05,
            "max_failures": 1,
            "temperature": 0.0
        },
        
        Environment.DEVELOPMENT: {
            "headless": False,
            "highlight_elements": True,
            "wait_between_actions": 2.0,
            "save_conversation_path": "./debug_conversations",
            "generate_gif": True
        },
        
        Environment.PRODUCTION: {
            "headless": True,
            "disable_security": False,
            "temperature": 0.0,
            "allowed_domains": ["specific-domains"]  # To be configured per deployment
        },
        
        Environment.WEB: {
            "headless": True,
            "enable_memory": True,
            "max_steps": 120,
            "save_conversation_path": "./web_conversations"
        },
        
        Environment.API: {
            "headless": True,
            "use_vision": False,
            "enable_memory": False,
            "max_steps": 40,
            "wait_between_actions": 0.1
        },
        
        Environment.LOAD: {
            "headless": True,
            "use_vision": False,
            "enable_memory": False,
            "max_steps": 15,
            "minimum_wait_page_load_time": 0.05,
            "wait_between_actions": 0.01
        },
        
        Environment.CAPTCHA: {
            "headless": False,
            "stealth": True,
            "wait_between_actions": 3.0,
            "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "allowed_domains": ["*.google.com", "*.recaptcha.net"],
            "keep_alive": True
        }
    }
    
    # Legacy configuration mapping (for backward compatibility)
    LEGACY_MAPPING = {
        "ci": (ConfigProfile.FAST, Environment.CI),
        "smoke": (ConfigProfile.BALANCED, None),
        "regression": (ConfigProfile.THOROUGH, None),
        "dev": (ConfigProfile.BALANCED, Environment.DEVELOPMENT),
        "prod": (ConfigProfile.BALANCED, Environment.PRODUCTION),
        "web": (ConfigProfile.BALANCED, Environment.WEB),
        "api": (ConfigProfile.FAST, Environment.API),
        "load": (ConfigProfile.FAST, Environment.LOAD),
        "captcha": (ConfigProfile.BALANCED, Environment.CAPTCHA)
    }

    def __init__(self, cache: Optional[CacheManager] = None):
        """Initialize configuration manager."""
        self.embedding_model = os.getenv('EMBEDDING_MODEL')
        self.cache = cache if cache is not None else cache_manager
        # Set a default TTL for configuration caching (e.g., 1 hour)
        self.cache_ttl_seconds = 3600
        
    def _generate_cache_key(
        self,
        config_id: Union[str, ConfigProfile],
        environment: Optional[Union[str, Environment]] = None,
        overrides: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generates a deterministic cache key."""
        key_parts = [f"id={config_id}", f"env={environment}"]
        if overrides:
            # Use JSON dump with sorted keys for a stable representation
            try:
                overrides_str = json.dumps(overrides, sort_keys=True)
                key_parts.append(f"overrides={overrides_str}")
            except TypeError:
                # Fallback for non-serializable objects, though this should be avoided
                key_parts.append(f"overrides={str(overrides)}")

        return "config:" + ":".join(key_parts)

    def resolve_config(
        self,
        config_id: Union[str, ConfigProfile],
        environment: Optional[Union[str, Environment]] = None,
        overrides: Optional[Dict[str, Any]] = None
    ) -> BrowserConfig:
        """
        Resolve configuration from profile, environment, and overrides.
        This method is cached to improve performance.
        
        Args:
            config_id: Base configuration profile or legacy config name
            environment: Target environment for additional overrides
            overrides: Custom overrides for specific requirements
            
        Returns:
            BrowserConfig: Fully resolved configuration
            
        Raises:
            ValueError: If configuration cannot be resolved
        """
        # 1. Generate cache key and check cache
        cache_key = self._generate_cache_key(config_id, environment, overrides)
        cached_config = self.cache.get(cache_key)
        if cached_config:
            return cached_config

        # 2. If not in cache, resolve config using existing logic
        try:
            # Handle legacy configuration names
            if isinstance(config_id, str) and config_id in self.LEGACY_MAPPING:
                profile, legacy_env = self.LEGACY_MAPPING[config_id]
                if environment is None:
                    environment = legacy_env
            else:
                profile = ConfigProfile(config_id)

            # Get base configuration
            if profile not in self.BASE_CONFIGS:
                raise ValueError(f"Unknown configuration profile: {profile}")

            base_config = self.BASE_CONFIGS[profile]

            # Apply environment-specific overrides
            config = base_config
            if environment:
                env = Environment(environment) if isinstance(environment, str) else environment
                if env in self.ENVIRONMENT_OVERRIDES:
                    config = config.apply_overrides(self.ENVIRONMENT_OVERRIDES[env])

            # Apply embedding model detection for memory settings
            if self.embedding_model:
                config = config.apply_overrides({"enable_memory": True})

            # Apply custom overrides
            if overrides:
                config = config.apply_overrides(overrides)
            
            # 3. Store the resolved config in cache and return
            self.cache.set(cache_key, config, ttl_seconds=self.cache_ttl_seconds)
            return config

        except (ValueError, TypeError) as e:
            # Re-raise exceptions after logging or handling
            # Note: We don't cache failures
            raise e

    def get_config_for_test_type(self, test_type: str) -> BrowserConfig:
        """
        Get optimized configuration for specific test types.
        
        Args:
            test_type: Type of test (smoke, full, suite, case, codegen)
            
        Returns:
            BrowserConfig: Optimized configuration for test type
        """
        test_type_configs = {
            "smoke": (ConfigProfile.BALANCED, None, {"max_steps": 30}),
            "full": (ConfigProfile.THOROUGH, None, {"generate_gif": True}),
            "suite": (ConfigProfile.BALANCED, None, {"wait_between_actions": 0.5}),
            "case": (ConfigProfile.BALANCED, None, {}),
            "codegen": (ConfigProfile.BALANCED, None, {"save_conversation_path": "./codegen_conversations"})
        }
        
        if test_type not in test_type_configs:
            # Default to balanced for unknown test types
            return self.resolve_config(ConfigProfile.BALANCED)
            
        profile, environment, overrides = test_type_configs[test_type]
        return self.resolve_config(profile, environment, overrides)
    
    def validate_config(self, config: BrowserConfig) -> bool:
        """
        Validate configuration for consistency and safety.
        
        Args:
            config: Configuration to validate
            
        Returns:
            bool: True if configuration is valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        # Validate max_steps
        if config.max_steps <= 0:
            raise ValueError("max_steps must be positive")
            
        # Validate wait times
        if config.wait_between_actions < 0:
            raise ValueError("wait_between_actions must be non-negative")
            
        if config.minimum_wait_page_load_time < 0:
            raise ValueError("minimum_wait_page_load_time must be non-negative")
            
        # Validate temperature
        if not 0 <= config.temperature <= 2.0:
            raise ValueError("temperature must be between 0 and 2.0")
            
        # Validate paths
        if config.save_conversation_path:
            path = Path(config.save_conversation_path)
            if not path.parent.exists():
                raise ValueError(f"Parent directory for save_conversation_path does not exist: {path.parent}")
        
        return True
    
    def get_available_profiles(self) -> list:
        """Get list of available configuration profiles."""
        return list(self.BASE_CONFIGS.keys())
    
    def get_available_environments(self) -> list:
        """Get list of available environment overrides.""" 
        return list(self.ENVIRONMENT_OVERRIDES.keys())
    
    def get_legacy_configs(self) -> list:
        """Return a list of all supported legacy configuration names."""
        return list(self.LEGACY_MAPPING.keys())


# Singleton instance for shared use
configuration_manager = ConfigurationManager()

def get_config(
    config_id: Union[str, ConfigProfile],
    environment: Optional[Union[str, Environment]] = None,
    overrides: Optional[Dict[str, Any]] = None
) -> BrowserConfig:
    """
    Convenience function to get a resolved configuration using the
    singleton ConfigurationManager instance.
    
    Args:
        config_id: Base configuration profile or legacy config name
        environment: Target environment for additional overrides
        overrides: Custom overrides for specific requirements
        
    Returns:
        BrowserConfig: Fully resolved configuration
    """
    return configuration_manager.resolve_config(config_id, environment, overrides)


def get_config_for_test(test_type: str) -> BrowserConfig:
    """
    Convenience function to get a configuration for a specific test type
    using the singleton ConfigurationManager instance.
    """
    return configuration_manager.get_config_for_test_type(test_type)