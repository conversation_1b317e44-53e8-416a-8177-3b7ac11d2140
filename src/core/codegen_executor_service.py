"""Servicio para ejecutar tests generados por Playwright CodeGen usando browser-use."""

import os
import asyncio
import json
import re
import uuid
import tempfile
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
from pathlib import Path
import base64

# Configurar path para browser_use local
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../libs'))

from browser_use import Browser, Agent as BrowserAgent
# Use the new unified LLM architecture instead of direct Gemini
from src.services.llm.llm_service_factory import get_llm_factory
from src.services.llm.base_llm_service import LLMRequest
from src.utilities.browser_helper import create_robust_config
from src.core.playwright_codegen_service import PlaywrightCodegenService
from src.utilities.utils import controller
from src.core.result_transformer import transform_agent_history_to_standard_result

logger = logging.getLogger(__name__)

class CodegenExecutorService:
    """Servicio para ejecutar tests de CodeGen usando browser-use."""
    
    def __init__(self, codegen_service=None):
        """Inicializa el servicio de ejecución.
        
        Args:
            codegen_service: Instancia compartida del servicio de Playwright CodeGen
        """
        logging.info(os.getenv("GOOGLE_API_KEY"))
        self.api_key = os.getenv("GOOGLE_API_KEY")
        # Initialize the new unified LLM architecture
        from src.services.llm.llm_service_factory import get_llm_factory
        from src.services.llm.base_llm_service import LLMRequest
        
        self.llm_factory = get_llm_factory()
        
        # Validate that the factory is properly initialized
        try:
            # Test the factory with a simple request
            test_request = LLMRequest(
                messages=[{"role": "user", "content": "test"}],
                use_case="code_generation",
                max_tokens=10
            )
            # Don't actually send the test request, just validate initialization
            logger.info("✅ LLM factory initialized successfully for code generation")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM factory: {e}")
            raise ValueError(f"Failed to initialize LLM factory: {e}")
            
        # Store configuration for reference
        self.model_config = {
            "temperature": 0.0,
            "max_tokens": 4096,
            "use_case": "code_generation"
        }
        
        logger.info(f"LLM initialized successfully with model: {model_name} ✅")
        
        # Usar la instancia compartida o crear una nueva si no se proporciona
        if codegen_service is None:
            from src.core.playwright_codegen_service import PlaywrightCodegenService
            self.codegen_service = PlaywrightCodegenService()
        else:
            self.codegen_service = codegen_service
            
        # Directorios para persistencia 
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.executions_dir = os.path.join(root_dir, "codegen_sessions", "executions")
        
        # Usar el mismo directorio que el API está sirviendo para screenshots
        self.screenshots_dir = os.path.join(tempfile.gettempdir(), "qak_codegen", "screenshots")
        
        # Crear directorios si no existen
        os.makedirs(self.executions_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # Cargar ejecuciones existentes
        self.active_executions = self._load_executions()
        
        # Flag to track if LLM has been tested
        self._llm_tested = False
    
    async def _ensure_llm_connection(self):
        """Ensure LLM connection is working before executing tests."""
        if self._llm_tested:
            return
            
        max_retries = 3
        base_delay = 2
        
        for attempt in range(max_retries):
            try:
                # Test the new LLM factory
                test_request = LLMRequest(
                    messages=[{"role": "user", "content": "Respond with: OK"}],
                    use_case="test",
                    max_tokens=10,
                    temperature=0.0
                )
                
                response = self.llm_factory.make_request(test_request)
                
                if not response.success or not response.content:
                    raise ValueError(f"LLM test failed: {response.error if not response.success else 'No content'}")
                    
                logger.info(f"LLM connection test successful on attempt {attempt + 1} ✅")
                self._llm_tested = True
                return
                    
            except Exception as e:
                logger.warning(f"LLM test attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise ValueError(f"LLM connection test failed after {max_retries} attempts: {e}")
                time.sleep(2)
                    
            except asyncio.CancelledError:
                logger.warning(f"LLM connection test was cancelled on attempt {attempt + 1}")
                if attempt == max_retries - 1:
                    raise ValueError("LLM connection test was cancelled after all retries")
                await asyncio.sleep(base_delay * (attempt + 1))
                continue
                
            except asyncio.TimeoutError as e:
                logger.warning(f"LLM connection test timeout on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    raise ValueError(f"LLM connection test timed out after {max_retries} attempts")
                await asyncio.sleep(base_delay * (attempt + 1))
                continue
                
            except Exception as e:
                error_msg = str(e).lower()
                is_retryable = any(keyword in error_msg for keyword in [
                    'cancelled', 'timeout', 'unavailable', 'deadline', 'grpc', 'network'
                ])
                
                if is_retryable and attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)
                    logger.warning(f"LLM connection test failed on attempt {attempt + 1}/{max_retries} (retryable): {e}")
                    logger.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                    continue
                else:
                    logger.error(f"LLM connection test failed on attempt {attempt + 1}: {e}")
                    
                    # Provide helpful error messages for common issues
                    if "api key" in error_msg or "unauthorized" in error_msg:
                        raise ValueError("Invalid Google API key. Please check your GOOGLE_API_KEY environment variable.")
                    elif "quota" in error_msg or "rate limit" in error_msg:
                        raise ValueError("Google AI API quota exceeded. Please check your usage or wait before retrying.")
                    elif "not found" in error_msg:
                        raise ValueError(f"Model not found. Please check if the model '{getattr(self.llm, 'model', getattr(self.llm, 'model_name', 'unknown'))}' is available.")
                    else:
                        raise ValueError(f"LLM connection test failed: {str(e)}")
        
        raise ValueError(f"LLM connection test failed after {max_retries} attempts")
    
    async def execute_codegen_test(
        self, 
        session_id: str,
        config_id: Optional[str] = None,
        configuration: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta un test generado por CodeGen usando browser-use.
        
        Args:
            session_id: ID de la sesión de CodeGen
            config_id: ID de configuración de browser (opcional)
            configuration: Configuración personalizada de browser (opcional)
            
        Returns:
            Dict con información de la ejecución
        """
        execution_id = str(uuid.uuid4())
        
        try:
            # Ensure LLM connection is working
            await self._ensure_llm_connection()
            
            # Obtener el código generado
            generated_code = await self.codegen_service.get_generated_code(session_id)
            if not generated_code:
                raise ValueError(f"No se encontró código generado para la sesión {session_id}")
            
            # Obtener información de la sesión
            session = await self.codegen_service.get_session(session_id)
            if not session:
                raise ValueError(f"Sesión {session_id} no encontrada")
            
            # Crear información de ejecución
            execution_info = {
                "execution_id": execution_id,
                "session_id": session_id,
                "status": "starting",
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "target_url": session.url,
                "generated_code": generated_code,
                "browser_config": configuration or {},
                "history": [],
                "result": None,
                "error": None
            }
            
            self.active_executions[execution_id] = execution_info
            self._save_execution(execution_id)  # Persistir ejecución inicial
            
            # Ejecutar en background
            asyncio.create_task(self._run_execution(execution_id, generated_code, session, config_id, configuration))
            
            logger.info(f"Ejecución de test CodeGen iniciada: {execution_id} para sesión {session_id}")
            return {
                "execution_id": execution_id,
                "status": "starting",
                "message": "Ejecución iniciada exitosamente"
            }
            
        except Exception as e:
            logger.error(f"Error iniciando ejecución para sesión {session_id}: {str(e)}")
            if execution_id in self.active_executions:
                self.active_executions[execution_id]["status"] = "failed"
                self.active_executions[execution_id]["error"] = str(e)
                self.active_executions[execution_id]["updated_at"] = datetime.now()
                self._save_execution(execution_id)  # Persistir error
            raise
    
    async def _run_execution(
        self,
        execution_id: str,
        generated_code: str,
        session,
        config_id: Optional[str],
        configuration: Optional[Dict[str, Any]]
    ):
        """Ejecuta el test en background."""
        execution = self.active_executions[execution_id]
        
        try:
            execution["status"] = "running"
            execution["updated_at"] = datetime.now()
            self._save_execution(execution_id)
            
            # Convertir código de Playwright a instrucciones para browser-use
            instructions = self._convert_playwright_to_instructions(generated_code, session.target_language)
            
            # Crear la configuración base del navegador
            browser_config = create_robust_config()
            
            # Actualizar con la configuración personalizada si existe
            if isinstance(configuration, dict):
                for key, value in configuration.items():
                    setattr(browser_config, key, value)
            
            # Crear directorio para screenshots de esta ejecución
            screenshots_dir = os.path.join(self.screenshots_dir, execution_id)
            os.makedirs(screenshots_dir, exist_ok=True)
            
            # Configurar parámetros del navegador
            browser_params = {
                "headless": getattr(browser_config, "headless", False),
                "user_data_dir": getattr(browser_config, "user_data_dir", None),
                "viewport_width": getattr(browser_config, "viewport_width", None),
                "viewport_height": getattr(browser_config, "viewport_height", None),
                "timezone": getattr(browser_config, "timezone", None),
                "locale": getattr(browser_config, "locale", None),
                "geolocation": getattr(browser_config, "geolocation", None),
                "user_agent": getattr(browser_config, "user_agent", None),
                "proxy": getattr(browser_config, "proxy", None)
            }
            
            # Limpiar None values para evitar errores
            browser_params = {k: v for k, v in browser_params.items() if v is not None}
            
            # Ejecutar con browser-use
            async with Browser(**browser_params) as browser:
                # Crear la tarea para el agente con las instrucciones
                task = {
                    "objective": "Execute test steps",
                    "instructions": instructions,
                    "context": {
                        "session_id": session.session_id,
                        "target_language": session.target_language,
                        "url": session.url
                    }
                }
                
                # Inicializar el agente con todos los parámetros requeridos
                import uuid
                unique_task_id = str(uuid.uuid4())[:8]  # Short unique ID for EventBus
                agent = BrowserAgent(
                    browser=browser,
                    llm=self.llm,
                    task=task,
                    task_id=unique_task_id,
                    calculate_cost=False  # Disable cost tracking to avoid compatibility issues
                )
                
                # Configurar directorio de screenshots después de la inicialización
                agent.screenshot_dir = screenshots_dir
                
                # Configurar parámetros de ejecución
                agent.max_steps = 50
                agent.timeout = 300
                agent.retry_on_error = True
                
                # Ejecutar instrucciones
                history = await agent.run()
                
                
                # Analizar resultado y guardar screenshots
                result = self._analyze_execution_result(history)
                screenshots = self._extract_screenshots_from_history(history, screenshots_dir)
                
                # Convertir rutas de screenshots a URLs
                screenshot_urls = []
                for screenshot_path in screenshots:
                    # Convertir ruta absoluta a URL relativa
                    relative_path = os.path.relpath(screenshot_path, screenshots_dir)
                    screenshot_url = f"/api/codegen-screenshots/{execution_id}/{relative_path}"
                    screenshot_urls.append(screenshot_url)
                
                # Extraer screenshots del historial como base64
                base64_screenshots = self._extract_base64_screenshots_from_history(history)

                # Combinar screenshots de archivos y base64
                all_screenshots = screenshot_urls + base64_screenshots

                # Analizar resultado
                execution["status"] = "completed"
                # Convertir AgentHistoryList a formato serializable
                try:
                    execution["history"] = self._convert_history_to_serializable(history)
                except Exception as history_error:
                    logger.warning(f"Error convirtiendo historial a serializable: {history_error}")
                    execution["history"] = {
                        "error": f"History conversion failed: {str(history_error)}",
                        "steps": [],
                        "summary": {},
                        "total_steps": len(history.history) if hasattr(history, 'history') and history.history else 0
                    }
                
                execution["result"] = result
                execution["screenshots"] = all_screenshots
                execution["completed_at"] = datetime.now()
                execution["updated_at"] = datetime.now()
                
                # Intentar guardar la ejecución
                try:
                    self._save_execution(execution_id)
                    logger.info(f"Ejecución {execution_id} guardada exitosamente")
                except Exception as save_error:
                    logger.error(f"Error guardando ejecución {execution_id}: {save_error}")
                    # Continuar sin marcar como fallida, ya que la ejecución en sí fue exitosa
            
        except Exception as e:
            logger.error(f"Error en ejecución {execution_id}: {str(e)}")

            # Extraer información detallada del error
            error_info = self._extract_error_info_from_logs(str(e))

            execution["status"] = "failed"
            execution["error"] = str(e)
            execution["error_details"] = error_info
            execution["updated_at"] = datetime.now()

            # Crear un resultado de error más informativo
            execution["result"] = {
                "success": False,
                "error": str(e),
                "analysis": f"❌ Ejecución falló: {error_info.get('message', str(e))}",
                "steps": [{
                    "step_number": 1,
                    "action": "Error de ejecución",
                    "description": f"❌ {error_info.get('message', str(e))}",
                    "status": "error",
                    "timestamp": datetime.now().isoformat(),
                    "details": {
                        "error": str(e),
                        "error_type": error_info.get('type', 'Unknown Error'),
                        "solution": error_info.get('solution', 'Intenta nuevamente o contacta soporte')
                    }
                }],
                "details": {
                    "steps_completed": 0,
                    "steps_failed": 1,
                    "execution_summary": f"Ejecución falló: {error_info.get('type', 'Error desconocido')}",
                    "error_info": error_info
                }
            }

            # Intentar guardar la ejecución con error
            try:
                self._save_execution(execution_id)
                logger.info(f"Ejecución fallida {execution_id} guardada para debugging")
            except Exception as save_error:
                logger.error(f"Error guardando ejecución fallida {execution_id}: {save_error}")
                # Crear un archivo de emergencia con información básica
                try:
                    emergency_data = {
                        "execution_id": execution_id,
                        "status": "failed",
                        "error": str(e),
                        "save_error": str(save_error),
                        "timestamp": datetime.now().isoformat()
                    }
                    emergency_file = os.path.join(self.executions_dir, f"emergency_{execution_id}.json")
                    with open(emergency_file, "w", encoding='utf-8') as f:
                        json.dump(emergency_data, f, indent=2, ensure_ascii=False)
                    logger.info(f"Archivo de emergencia creado: {emergency_file}")
                except Exception as emergency_error:
                    logger.error(f"Error creando archivo de emergencia: {emergency_error}")

    def _convert_playwright_to_instructions(self, code: str, language: str) -> str:
        """Convierte código de Playwright a instrucciones para browser-use.

        Args:
            code: Código generado por Playwright CodeGen
            language: Lenguaje del código (javascript, python, etc.)

        Returns:
            Instrucciones en lenguaje natural para browser-use
        """
        try:
            # Extraer acciones del código de Playwright
            actions = []
            
            if language in ["javascript", "typescript"]:
                actions = self._extract_actions_from_js(code)
            elif language == "python":
                actions = self._extract_actions_from_python(code)
            else:
                # Fallback: intentar extraer de cualquier formato
                actions = self._extract_actions_generic(code)
            
            # Convertir acciones a instrucciones
            instructions = self._actions_to_instructions(actions)
            
            return instructions
            
        except Exception as e:
            logger.warning(f"Error convirtiendo código a instrucciones: {str(e)}")
            # Fallback: usar el código como está con instrucciones básicas
            return f"""
            Ejecuta las siguientes acciones de prueba basadas en este código:
            
            {code}
            
            Por favor realiza cada acción paso a paso y verifica que se ejecute correctamente.
            """
    
    def _extract_actions_from_js(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código JavaScript/TypeScript."""
        actions = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # page.goto()
            if 'page.goto(' in line:
                url_match = re.search(r"page\.goto\(['\"]([^'\"]+)['\"]", line)
                if url_match:
                    actions.append({
                        "type": "navigate",
                        "url": url_match.group(1)
                    })
            
            # page.click()
            elif 'page.click(' in line or '.click()' in line:
                selector = None

                # Buscar el selector en getByRole, getByText, etc.
                if 'getByRole(' in line:
                    # Para getByRole('button', { name: 'Iniciar Sesión' })
                    role_match = re.search(r"getByRole\(['\"]([^'\"]+)['\"],\s*\{\s*name:\s*['\"]([^'\"]+)['\"]", line)
                    if role_match:
                        selector = f"{role_match.group(1)} '{role_match.group(2)}'"
                elif 'getByText(' in line:
                    # Para getByText('Click me')
                    text_match = re.search(r"getByText\(['\"]([^'\"]+)['\"]", line)
                    if text_match:
                        selector = f"texto '{text_match.group(1)}'"
                elif 'getByLabel(' in line:
                    # Para getByLabel('Submit')
                    label_match = re.search(r"getByLabel\(['\"]([^'\"]+)['\"]", line)
                    if label_match:
                        selector = f"elemento '{label_match.group(1)}'"
                else:
                    # Fallback: usar el primer string encontrado
                    selector_match = re.search(r"['\"]([^'\"]+)['\"]", line)
                    if selector_match:
                        selector = selector_match.group(1)

                if selector:
                    actions.append({
                        "type": "click",
                        "selector": selector
                    })
            
            # page.fill() or page.type()
            elif 'page.fill(' in line or 'page.type(' in line or '.fill(' in line:
                # Mejorar el parsing para manejar correctamente la sintaxis de Playwright
                selector = None
                text_value = None

                # Buscar el patrón .fill('valor') al final de la línea
                fill_match = re.search(r"\.fill\(['\"]([^'\"]+)['\"]\)", line)
                if fill_match:
                    text_value = fill_match.group(1)

                # Buscar el selector en getByRole, getByLabel, etc.
                if 'getByRole(' in line:
                    # Para getByRole('textbox', { name: 'Email' })
                    role_match = re.search(r"getByRole\(['\"]([^'\"]+)['\"],\s*\{\s*name:\s*['\"]([^'\"]+)['\"]", line)
                    if role_match:
                        selector = f"{role_match.group(1)} ({role_match.group(2)})"
                elif 'getByLabel(' in line:
                    # Para getByLabel('Email')
                    label_match = re.search(r"getByLabel\(['\"]([^'\"]+)['\"]", line)
                    if label_match:
                        selector = label_match.group(1)
                elif 'getByPlaceholder(' in line:
                    # Para getByPlaceholder('Enter email')
                    placeholder_match = re.search(r"getByPlaceholder\(['\"]([^'\"]+)['\"]", line)
                    if placeholder_match:
                        selector = placeholder_match.group(1)
                else:
                    # Fallback: usar el primer string encontrado como selector
                    all_strings = re.findall(r"['\"]([^'\"]+)['\"]", line)
                    if len(all_strings) >= 2:
                        # El último string suele ser el valor a llenar
                        selector = all_strings[-2] if len(all_strings) > 2 else all_strings[0]

                if selector and text_value:
                    actions.append({
                        "type": "fill",
                        "selector": selector,
                        "text": text_value
                    })
            
            # page.selectOption()
            elif 'selectOption(' in line:
                parts = re.findall(r"['\"]([^'\"]+)['\"]", line)
                if len(parts) >= 1:
                    actions.append({
                        "type": "select",
                        "option": parts[-1]
                    })
        
        return actions
    
    def _extract_actions_from_python(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código Python."""
        actions = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # page.goto()
            if 'page.goto(' in line:
                url_match = re.search(r"page\.goto\(['\"]([^'\"]+)['\"]", line)
                if url_match:
                    actions.append({
                        "type": "navigate",
                        "url": url_match.group(1)
                    })
            
            # Similar patterns for Python...
            elif 'page.click(' in line or '.click()' in line:
                selector_match = re.search(r"['\"]([^'\"]+)['\"]", line)
                if selector_match:
                    actions.append({
                        "type": "click",
                        "selector": selector_match.group(1)
                    })
        
        return actions
    
    def _extract_actions_generic(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código de cualquier formato."""
        actions = []
        
        # Patrones genéricos para encontrar URLs
        url_pattern = r"https?://[^\s'\"]+|['\"]https?://[^'\"]+['\"]"
        urls = re.findall(url_pattern, code)
        
        if urls:
            # Limpiar URL
            url = urls[0].strip('\'"')
            actions.append({
                "type": "navigate",
                "url": url
            })
        
        # Patrones para clicks y fills...
        click_patterns = [
            r"click\(['\"]([^'\"]+)['\"]",
            r"getByRole\(['\"]([^'\"]+)['\"]",
            r"getByText\(['\"]([^'\"]+)['\"]"
        ]
        
        for pattern in click_patterns:
            matches = re.findall(pattern, code, re.IGNORECASE)
            for match in matches:
                actions.append({
                    "type": "click",
                    "selector": match
                })
        
        return actions
    
    def _actions_to_instructions(self, actions: List[Dict[str, Any]]) -> str:
        """Convierte acciones extraídas a instrucciones en lenguaje natural."""
        if not actions:
            return "Realiza las acciones de prueba según el código proporcionado."
        
        instructions = []
        instructions.append("Ejecuta las siguientes acciones paso a paso:")
        
        for i, action in enumerate(actions, 1):
            if action["type"] == "navigate":
                instructions.append(f"{i}. Navega a la URL: {action['url']}")
            elif action["type"] == "click":
                instructions.append(f"{i}. Haz clic en el elemento: {action['selector']}")
            elif action["type"] == "fill":
                instructions.append(f"{i}. Rellena el campo '{action['selector']}' con el texto '{action['text']}'")
            elif action["type"] == "select":
                instructions.append(f"{i}. Selecciona la opción: {action['option']}")
        
        instructions.append("\nVerifica que cada acción se ejecute correctamente antes de continuar con la siguiente.")
        
        return "\n".join(instructions)
    
    def _analyze_execution_result(self, history) -> Dict[str, Any]:
        """Analiza el resultado de la ejecución del agente."""
        
        if not history or not hasattr(history, 'history') or not history.history:
            return {
                "status": "failed",
                "summary": "No history recorded.",
                "error": "Execution did not produce any history.",
                "steps": []
            }

        # AgentHistoryList tiene un atributo .history que es una lista
        steps = history.history 
        
        # Obtener resumen
        summary_data = history.get_summary()

        step_details = []
        has_critical_error = False
        last_error = None

        for i, step in enumerate(steps):
            step_info = {
                "step": i + 1,
                "goal": None,
                "action": None,
                "result": None,
                "error": None,
                "screenshot": None,
                "duration_seconds": None,
                "tokens": {
                    "input": None,
                    "output": None,
                    "cost": None
                }
            }

            if step.model_output and step.model_output.current_state:
                step_info["goal"] = step.model_output.current_state.next_goal
                
                if step.model_output.action:
                    # Formatear la acción para que sea legible
                    try:
                        action_data = step.model_output.action[0].model_dump(exclude_unset=True)
                        action_name = list(action_data.keys())[0] if action_data else "unknown"
                        action_params = action_data.get(action_name, {})
                        step_info["action"] = f"{action_name}({json.dumps(action_params)})"
                    except Exception:
                        step_info["action"] = "Could not parse action"

            if step.result:
                # Concatenar resultados y errores de múltiples resultados de acción
                all_results = [r.extracted_content for r in step.result if r.extracted_content]
                all_errors = [r.error for r in step.result if r.error]
                
                if all_results:
                    step_info["result"] = " | ".join(all_results)
                
                if all_errors:
                    step_info["error"] = " | ".join(all_errors)
                    last_error = step_info["error"]
                    if "Browser closed" in last_error:
                        has_critical_error = True

            if step.state and step.state.screenshot:
                # Asumimos que el screenshot está en base64
                step_info["screenshot"] = step.state.screenshot

            if step.metadata:
                step_info["duration_seconds"] = step.metadata.duration_seconds
                step_info["tokens"]["input"] = step.metadata.input_tokens
                step_info["tokens"]["output"] = step.metadata.output_tokens
                step_info["tokens"]["cost"] = step.metadata.cost

            step_details.append(step_info)
        
        # Determinar estado final
        final_status = "completed"
        if has_critical_error:
            final_status = "failed"
        elif summary_data.get('has_errors', False):
            final_status = "failed"
        elif not summary_data.get('is_successful', False):
             final_status = "completed_with_failures"


        return {
            "status": final_status,
            "summary": summary_data if isinstance(summary_data, dict) else {},
            "error": last_error,
            "steps": step_details,
            "final_result": self._safe_extract_final_result(history)
        }

    def get_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene información de una ejecución (activa o completada)."""
        execution = None
        
        # Primero check ejecuciones activas en memoria
        if execution_id in self.active_executions:
            execution = self.active_executions[execution_id].copy()
        else:
            # Si no está activa, buscar en archivos guardados
            execution_file = os.path.join(self.executions_dir, f"execution_{execution_id}.json")
            if os.path.exists(execution_file):
                try:
                    with open(execution_file, "r") as f:
                        execution = json.load(f)
                        
                    # Convertir datetime strings a datetime objects para compatibilidad
                    for date_field in ["created_at", "updated_at", "completed_at"]:
                        if date_field in execution and execution[date_field]:
                            try:
                                execution[date_field] = datetime.fromisoformat(execution[date_field])
                            except:
                                # Si falla la conversión, mantener el string
                                pass
                    
                except Exception as e:
                    logger.error(f"Error loading execution {execution_id}: {e}")
                    return None
        
        # Transformar los datos al formato StandardResult estándar
        if execution:
            logger.info(f"[GET_EXECUTION] About to transform execution {execution_id}")
            execution = self._transform_execution_to_standard_result(execution)
            logger.info(f"[GET_EXECUTION] Transformation completed for {execution_id}")
            
        return execution
    
    def list_executions(self) -> List[Dict[str, Any]]:
        """Lista todas las ejecuciones activas."""
        return [
            {
                "execution_id": exec_id,
                "session_id": exec_info["session_id"],
                "status": exec_info["status"],
                "created_at": exec_info["created_at"].isoformat(),
                "updated_at": exec_info["updated_at"].isoformat(),
                "target_url": exec_info.get("target_url")
            }
            for exec_id, exec_info in self.active_executions.items()
        ]

    
    async def stop_execution(self, execution_id: str) -> bool:
        """Detiene una ejecución activa."""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        if execution["status"] in ["completed", "failed", "stopped"]:
            return True
        
        execution["status"] = "stopped"
        execution["updated_at"] = datetime.now()
        self._save_execution(execution_id)  # Persistir detención
        
        logger.info(f"Ejecución {execution_id} detenida")
        return True

    def _extract_base64_screenshots_from_history(self, history) -> List[str]:
        """Extrae screenshots en formato base64 del historial de ejecución de browser-use.

        Args:
            history: Historial de ejecución de browser-use

        Returns:
            Lista de screenshots en formato data URL (data:image/png;base64,...)
        """
        screenshots = []

        try:
            # Obtener los elementos del historial
            if hasattr(history, 'history'):
                history_items = history.history
                logger.info(f"DEBUG Screenshots: history.history tiene {len(history_items)} elementos")
            elif isinstance(history, list):
                history_items = history
                logger.info(f"DEBUG Screenshots: history es lista con {len(history_items)} elementos")
            else:
                logger.warning("No se puede acceder al historial para extraer screenshots base64")
                return screenshots

            for i, item in enumerate(history_items):
                logger.info(f"DEBUG Screenshots: Procesando step {i + 1}, tipo: {type(item)}")
                try:
                    # Buscar screenshots en objetos AgentHistory
                    if hasattr(item, 'result') and item.result:
                        logger.info(f"Step {i + 1}: Tiene result con {len(item.result)} elementos")
                        for j, result_item in enumerate(item.result):
                            if hasattr(result_item, 'screenshot') and result_item.screenshot:
                                screenshot_b64 = result_item.screenshot
                                logger.debug(f"Step {i + 1}, result {j}: Screenshot encontrado, longitud: {len(screenshot_b64)}")
                                if screenshot_b64 and len(screenshot_b64) > 100:  # Verificar que no esté vacío
                                    # Convertir a data URL si no lo es ya
                                    if not screenshot_b64.startswith('data:image'):
                                        screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                    else:
                                        screenshot_data_url = screenshot_b64
                                    screenshots.append(screenshot_data_url)
                                    logger.info(f"Screenshot extraído del step {i + 1}")
                            else:
                                logger.debug(f"Step {i + 1}, result {j}: No tiene screenshot o está vacío")

                    # Fallback: buscar en diccionarios si es JSON estático
                    elif isinstance(item, dict):
                        # Buscar screenshot directamente en el step
                        if 'screenshot' in item:
                            screenshot_b64 = item['screenshot']
                            if screenshot_b64 and len(screenshot_b64) > 100:
                                # Convertir a data URL si no lo es ya
                                if not screenshot_b64.startswith('data:image'):
                                    screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                else:
                                    screenshot_data_url = screenshot_b64
                                screenshots.append(screenshot_data_url)
                                logger.info(f"Screenshot extraído del step {i + 1} (JSON)")

                        # Buscar screenshot en el campo state
                        elif 'state' in item and isinstance(item['state'], dict) and 'screenshot' in item['state']:
                            screenshot_b64 = item['state']['screenshot']
                            if screenshot_b64 and len(screenshot_b64) > 100:
                                # Convertir a data URL si no lo es ya
                                if not screenshot_b64.startswith('data:image'):
                                    screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                else:
                                    screenshot_data_url = screenshot_b64
                                screenshots.append(screenshot_data_url)
                                logger.info(f"Screenshot extraído del step {i + 1} (JSON)")

                        # También buscar en result como fallback
                        result = item.get('result', [])
                        if isinstance(result, list):
                            for j, result_item in enumerate(result):
                                if isinstance(result_item, dict) and 'screenshot' in result_item:
                                    screenshot_b64 = result_item['screenshot']
                                    logger.info(f"Step {i + 1}, result {j}: Screenshot encontrado en result, longitud: {len(screenshot_b64) if screenshot_b64 else 0}")
                                    if screenshot_b64 and len(screenshot_b64) > 100:
                                        if not screenshot_b64.startswith('data:image'):
                                            screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                        else:
                                            screenshot_data_url = screenshot_b64
                                        screenshots.append(screenshot_data_url)
                                        logger.info(f"Screenshot extraído del step {i + 1} (JSON) - desde result")
                    else:
                        logger.debug(f"Step {i + 1}: No es dict")

                except Exception as e:
                    logger.warning(f"Error extrayendo screenshot del step {i}: {str(e)}")
                    continue

            logger.info(f"Extraídos {len(screenshots)} screenshots base64 del historial")
            return screenshots

        except Exception as e:
            logger.error(f"Error extrayendo screenshots base64 del historial: {str(e)}")
            return screenshots

    def update_execution_with_screenshots_from_json(self, execution_id: str, json_file_path: str):
        """Actualiza una ejecución existente con screenshots extraídos de un archivo JSON.

        Args:
            execution_id: ID de la ejecución a actualizar
            json_file_path: Ruta al archivo JSON con el historial
        """
        try:
            if execution_id not in self.active_executions:
                logger.warning(f"Ejecución {execution_id} no encontrada")
                return

            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # Extraer screenshots del historial JSON
            history_data = json_data.get("history", {})
            logger.info(f"DEBUG: history_data keys: {list(history_data.keys()) if isinstance(history_data, dict) else 'Not a dict'}")

            if isinstance(history_data, dict) and "history" in history_data:
                history_list = history_data["history"]
                logger.info(f"DEBUG: history_list length: {len(history_list) if isinstance(history_list, list) else 'Not a list'}")

                if isinstance(history_list, list) and len(history_list) > 0:
                    logger.info(f"DEBUG: First history item keys: {list(history_list[0].keys()) if isinstance(history_list[0], dict) else 'Not a dict'}")

                    # Buscar screenshots en el primer elemento para debug
                    first_item = history_list[0]
                    if isinstance(first_item, dict):
                        result = first_item.get('result', [])
                        logger.info(f"DEBUG: First item result length: {len(result) if isinstance(result, list) else 'Not a list'}")
                        if isinstance(result, list) and len(result) > 0:
                            result_item = result[0]
                            logger.info(f"DEBUG: First result item keys: {list(result_item.keys()) if isinstance(result_item, dict) else 'Not a dict'}")
                            if isinstance(result_item, dict) and 'screenshot' in result_item:
                                screenshot_len = len(result_item['screenshot']) if result_item['screenshot'] else 0
                                logger.info(f"DEBUG: Screenshot found, length: {screenshot_len}")

                # Simular el objeto history con la estructura esperada
                class MockHistory:
                    def __init__(self, history_list):
                        self.history = history_list

                mock_history = MockHistory(history_data["history"])
                base64_screenshots = self._extract_base64_screenshots_from_history(mock_history)

                # Actualizar la ejecución con los screenshots
                execution = self.active_executions[execution_id]
                existing_screenshots = execution.get("screenshots", [])

                # Combinar screenshots existentes con los nuevos
                all_screenshots = existing_screenshots + base64_screenshots
                execution["screenshots"] = all_screenshots

                logger.info(f"Ejecución {execution_id} actualizada con {len(base64_screenshots)} screenshots del JSON")
            else:
                logger.warning(f"No se encontró historial válido en el JSON")

        except Exception as e:
            logger.error(f"Error actualizando ejecución con screenshots del JSON: {str(e)}")

    def create_test_execution_from_json(self, json_file_path: str, session_id: Optional[str] = None) -> str:
        """Crea una ejecución de prueba desde un archivo JSON existente."""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            execution_id = json_data.get("execution_id", str(uuid.uuid4()))

            # Crear información de ejecución basada en el JSON
            final_session_id = session_id if session_id is not None else json_data.get("session_id", "test-session")
            execution_info = {
                "execution_id": execution_id,
                "session_id": final_session_id,
                "status": "completed",  # Siempre completed para ejecuciones de prueba
                "created_at": datetime.fromisoformat(json_data.get("created_at", datetime.now().isoformat())),
                "updated_at": datetime.fromisoformat(json_data.get("updated_at", datetime.now().isoformat())),
                "completed_at": datetime.fromisoformat(json_data.get("completed_at", datetime.now().isoformat())) if json_data.get("completed_at") else None,
                "target_url": json_data.get("target_url"),
                "generated_code": json_data.get("generated_code", ""),
                "browser_config": json_data.get("browser_config", {}),
                "history": json_data.get("history", {}),
                "screenshots": [],  # Inicializar vacío, se llenará después
                "result": None,
                "error": json_data.get("error")
            }

            # Analizar el historial para generar los steps
            history_data = json_data.get("history", {})
            if isinstance(history_data, dict) and "history" in history_data:
                # Simular el objeto history con la estructura esperada
                class MockHistory:
                    def __init__(self, history_list):
                        self.history = history_list

                mock_history = MockHistory(history_data["history"])
                execution_info["result"] = self._analyze_execution_result(mock_history)

                # Extraer screenshots del historial
                base64_screenshots = self._extract_base64_screenshots_from_history(mock_history)
                execution_info["screenshots"] = base64_screenshots

            # Agregar a ejecuciones activas
            self.active_executions[execution_id] = execution_info

            logger.info(f"Ejecución de prueba creada desde JSON: {execution_id} con {len(execution_info['screenshots'])} screenshots")
            return execution_id

        except Exception as e:
            logger.error(f"Error creando ejecución de prueba desde JSON: {str(e)}")
            raise
    
    def _extract_screenshots_from_history(self, history, screenshots_dir: str) -> List[str]:
        """Extrae screenshots del historial de browser-use y los guarda en el directorio especificado.
        
        Args:
            history: Historial de ejecución de browser-use
            screenshots_dir: Directorio donde guardar los screenshots
            
        Returns:
            Lista de rutas de los screenshots guardados
        """
        screenshots = []
        
        try:
            # Browser-use puede tener screenshots en diferentes formatos
            # Vamos a intentar extraer de las diferentes formas posibles
            
            if hasattr(history, 'history'):
                history_items = history.history
            elif isinstance(history, list):
                history_items = history
            else:
                # Si no podemos acceder al historial, crear screenshot del estado final
                logger.warning("No se puede acceder al historial para extraer screenshots")
                return screenshots
            
            # Recorrer los items del historial
            for i, item in enumerate(history_items):
                try:
                    # Browser-use puede almacenar screenshots en diferentes campos
                    screenshot_data = None
                    
                    # Intentar diferentes formas de acceder a los screenshots
                    if hasattr(item, 'screenshot'):
                        screenshot_data = item.screenshot
                    elif isinstance(item, dict):
                        screenshot_data = item.get('screenshot') or item.get('image') or item.get('screenshot_data')
                    
                    if screenshot_data:
                        # Guardar el screenshot
                        screenshot_path = os.path.join(screenshots_dir, f"step_{i+1}.png")
                        
                        # Si es base64, decodificar y guardar
                        if isinstance(screenshot_data, str):
                            # Remover prefijo data:image si existe
                            if screenshot_data.startswith('data:image'):
                                screenshot_data = screenshot_data.split(',')[1]
                            
                            with open(screenshot_path, "wb") as f:
                                f.write(base64.b64decode(screenshot_data))
                        elif isinstance(screenshot_data, bytes):
                            with open(screenshot_path, "wb") as f:
                                f.write(screenshot_data)
                        else:
                            continue
                        
                        screenshots.append(screenshot_path)
                        logger.debug(f"Screenshot guardado: {screenshot_path}")
                        
                except Exception as e:
                    logger.warning(f"Error procesando screenshot {i}: {str(e)}")
                    continue
            
            # Si no encontramos screenshots en el historial, intentar crear uno del estado final
            if not screenshots:
                logger.info("No se encontraron screenshots en el historial, esto es normal para browser-use")
                # Browser-use no siempre genera screenshots automáticamente
                # Los screenshots son opcionales y dependen de la configuración
            
        except Exception as e:
            logger.error(f"Error extrayendo screenshots del historial: {str(e)}")
        
        return screenshots
    
    def _load_executions(self) -> Dict[str, Any]:
        """Carga las ejecuciones existentes desde el sistema de archivos."""
        executions = {}
        
        try:
            # Cargar desde el archivo central
            central_file = os.path.join(self.executions_dir, "executions.json")
            if os.path.exists(central_file):
                with open(central_file, "r") as f:
                    executions = json.load(f)
                    
                    # Convertir strings de fecha a datetime
                    for exec_id, exec_info in executions.items():
                        for date_field in ["created_at", "updated_at", "completed_at"]:
                            if date_field in exec_info and exec_info[date_field]:
                                exec_info[date_field] = datetime.fromisoformat(exec_info[date_field])
            
            # Cargar ejecuciones individuales y hacer merge
            for file in Path(self.executions_dir).glob("execution_*.json"):
                try:
                    with open(file, "r") as f:
                        execution = json.load(f)
                        if "execution_id" in execution:
                            exec_id = execution["execution_id"]
                            
                            # Convertir strings de fecha a datetime
                            for date_field in ["created_at", "updated_at", "completed_at"]:
                                if date_field in execution and execution[date_field]:
                                    execution[date_field] = datetime.fromisoformat(execution[date_field])
                                    
                            executions[exec_id] = execution
                except Exception as e:
                    logger.warning(f"Error loading execution file {file}: {str(e)}")
                    
            # Limpiar ejecuciones antiguas (>30 días)
            now = datetime.now()
            executions = {
                exec_id: exec_info 
                for exec_id, exec_info in executions.items()
                if (now - exec_info["created_at"]).days <= 30
            }
            
        except Exception as e:
            logger.error(f"Error loading executions: {str(e)}")
        
        return executions
    
    def _save_execution(self, execution_id: str):
        """Guarda una ejecución en el sistema de archivos."""
        try:
            execution = self.active_executions[execution_id]
            
            # Crear una copia profunda y serializable de la ejecución
            execution_json = self._make_execution_serializable(execution.copy())
            
            # Guardar archivo individual
            file_path = os.path.join(self.executions_dir, f"execution_{execution_id}.json")
            with open(file_path, "w", encoding='utf-8') as f:
                json.dump(execution_json, f, indent=2, ensure_ascii=False, default=str)
            
            # Actualizar archivo central
            central_file = os.path.join(self.executions_dir, "executions.json")
            all_executions = {}
            
            if os.path.exists(central_file):
                try:
                    with open(central_file, "r", encoding='utf-8') as f:
                        all_executions = json.load(f)
                except json.JSONDecodeError:
                    logger.warning(f"Archivo central corrupto, recreando: {central_file}")
                    all_executions = {}
            
            all_executions[execution_id] = execution_json
            
            with open(central_file, "w", encoding='utf-8') as f:
                json.dump(all_executions, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"Error saving execution {execution_id}: {str(e)}")
            # Intentar guardar una versión mínima para debugging
            try:
                minimal_execution = {
                    "execution_id": execution_id,
                    "status": execution.get("status", "unknown"),
                    "error": str(e),
                    "created_at": execution.get("created_at", datetime.now()).isoformat() if isinstance(execution.get("created_at"), datetime) else str(execution.get("created_at", datetime.now())),
                    "updated_at": datetime.now().isoformat()
                }
                file_path = os.path.join(self.executions_dir, f"execution_{execution_id}_minimal.json")
                with open(file_path, "w", encoding='utf-8') as f:
                    json.dump(minimal_execution, f, indent=2, ensure_ascii=False)
                logger.info(f"Guardada versión mínima de ejecución: {file_path}")
            except Exception as minimal_error:
                logger.error(f"Error guardando versión mínima: {minimal_error}")

    def _safe_extract_final_result(self, history) -> str:
        """Safely extracts and converts the final result to a serializable string."""
        try:
            if not history:
                return "No history available"
            
            # Try to get the final result from various locations in browser-use history
            final_result = None
            
            # Method 1: Check if history has a direct final result
            if hasattr(history, 'final_result'):
                final_result = history.final_result
            
            # Method 2: Check the last step for done action or completion
            elif hasattr(history, 'history') and history.history:
                last_step = history.history[-1]
                
                # Check model_output.action for done action
                if hasattr(last_step, 'model_output') and last_step.model_output:
                    model_output = last_step.model_output
                    if hasattr(model_output, 'action') and model_output.action:
                        action = model_output.action
                        if hasattr(action, 'done') or (hasattr(action, 'action_type') and 'done' in str(action.action_type).lower()):
                            # Extract success status and message from done action
                            if hasattr(action, 'success'):
                                success = action.success
                                message = getattr(action, 'message', '') or getattr(action, 'text', '')
                                final_result = f"Completed with success={success}. {message}".strip()
                            elif hasattr(action, 'text'):
                                final_result = f"Completed: {action.text}"
                            else:
                                final_result = "Test execution completed"
                
                # Method 3: Check result field of last step
                if not final_result and hasattr(last_step, 'result') and last_step.result:
                    if isinstance(last_step.result, list) and last_step.result:
                        last_result = last_step.result[-1]
                        if hasattr(last_result, 'extracted_content'):
                            final_result = last_result.extracted_content
                        elif hasattr(last_result, 'success'):
                            final_result = f"Success: {last_result.success}"
            
            # Method 4: Check summary for final status
            if not final_result and hasattr(history, 'get_summary'):
                try:
                    summary = history.get_summary()
                    if summary and hasattr(summary, 'is_successful'):
                        final_result = f"Execution completed successfully: {summary.is_successful}"
                except:
                    pass
            
            # Fallback: Use string representation if available
            if not final_result:
                if hasattr(history, '__str__'):
                    str_repr = str(history)
                    if len(str_repr) < 500:  # Only if it's not too long
                        final_result = str_repr
                    else:
                        final_result = "Test execution completed - see step details for more information"
                else:
                    final_result = "Test execution completed"
            
            # Ensure result is a string and not too long
            if not isinstance(final_result, str):
                final_result = str(final_result)
            
            # Truncate if too long
            if len(final_result) > 1000:
                final_result = final_result[:997] + "..."
            
            return final_result
            
        except Exception as e:
            logger.warning(f"Error extracting final result: {str(e)}")
            return f"Test execution completed (extraction error: {str(e)})"

    def _make_execution_serializable(self, execution: Dict[str, Any]) -> Dict[str, Any]:
        """Convierte una ejecución a un formato completamente JSON serializable."""
        try:
            # Convertir datetimes a strings
            for date_field in ["created_at", "updated_at", "completed_at"]:
                if date_field in execution and execution[date_field]:
                    if isinstance(execution[date_field], datetime):
                        execution[date_field] = execution[date_field].isoformat()
                    else:
                        execution[date_field] = str(execution[date_field])
            
            # Asegurar que el historial esté en formato serializable
            if "history" in execution and execution["history"]:
                # Si el historial no está en formato dict, convertirlo
                if hasattr(execution["history"], 'history'):
                    # Es un objeto AgentHistoryList, convertir
                    execution["history"] = self._convert_history_to_serializable(execution["history"])
                elif not isinstance(execution["history"], dict):
                    # Si no es un dict, intentar convertir a string como fallback
                    execution["history"] = {"error": "Could not serialize history", "raw": str(execution["history"])}
            
            # Limpiar campos que podrían no ser serializables
            if "result" in execution and execution["result"]:
                execution["result"] = self._clean_result_for_serialization(execution["result"])
            
            # Convertir cualquier objeto restante a string
            execution = self._deep_serialize_objects(execution)
            
            return execution
            
        except Exception as e:
            logger.error(f"Error making execution serializable: {str(e)}")
            # Fallback a estructura mínima
            return {
                "execution_id": execution.get("execution_id", "unknown"),
                "status": execution.get("status", "error"),
                "error": f"Serialization failed: {str(e)}",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }

    def _clean_result_for_serialization(self, result: Any) -> Any:
        """Limpia el resultado para que sea serializable."""
        if isinstance(result, dict):
            cleaned = {}
            for key, value in result.items():
                try:
                    # Intentar serializar el valor para verificar si es válido
                    json.dumps(value, default=str)
                    cleaned[key] = value
                except (TypeError, ValueError):
                    # Si no se puede serializar, convertir a string
                    cleaned[key] = str(value)
            return cleaned
        elif isinstance(result, list):
            return [self._clean_result_for_serialization(item) for item in result]
        else:
            try:
                # Intentar serializar directamente
                json.dumps(result, default=str)
                return result
            except (TypeError, ValueError):
                return str(result)

    def _deep_serialize_objects(self, obj: Any) -> Any:
        """Convierte recursivamente cualquier objeto no serializable a string."""
        if isinstance(obj, dict):
            return {key: self._deep_serialize_objects(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._deep_serialize_objects(item) for item in obj]
        elif isinstance(obj, (str, int, float, bool, type(None))):
            return obj
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            # Convertir cualquier otro objeto a string
            return str(obj)

    def _clean_action_text(self, text: str) -> str:
        """Limpia y formatea el texto de la acción para que sea más legible."""
        if not text:
            return "Acción desconocida"

        # Remover prefijos comunes y limpiar texto
        text = text.strip()

        # Mapear acciones comunes a texto más claro en español
        action_mappings = {
            "click_element_by_index": "Hacer clic en elemento",
            "input_text": "Escribir texto",
            "navigate": "Navegar a página",
            "scroll": "Desplazar página",
            "wait": "Esperar",
            "take_screenshot": "Capturar pantalla",
            "fill": "Rellenar campo",
            "select": "Seleccionar opción",
            "press": "Presionar tecla",
            "hover": "Pasar cursor sobre elemento"
        }

        # Buscar mapeos conocidos
        text_lower = text.lower()
        for key, value in action_mappings.items():
            if key in text_lower:
                return value

        # Extraer información útil de textos largos
        if "click" in text_lower:
            if "button" in text_lower:
                return "Hacer clic en botón"
            elif "link" in text_lower:
                return "Hacer clic en enlace"
            else:
                return "Hacer clic en elemento"
        elif "input" in text_lower or "type" in text_lower:
            return "Escribir texto"
        elif "navigate" in text_lower or "goto" in text_lower:
            return "Navegar a página"

        # Si es muy largo, truncar pero mantener información útil
        if len(text) > 80:
            text = text[:77] + "..."

        return text

    def _extract_error_info_from_logs(self, logs: str) -> Dict[str, str]:
        """Extrae información de error de los logs de browser-use."""
        error_info = {}

        try:
            # Buscar patrones comunes de error en los logs
            lines = logs.split('\n')

            for i, line in enumerate(lines):
                # Buscar errores de cuota de API
                if "ResourceExhausted" in line and "quota" in line.lower():
                    error_info["type"] = "API Quota Exceeded"
                    error_info["message"] = "Se excedió la cuota de la API de Google Gemini"
                    error_info["solution"] = "Espera unos minutos antes de intentar nuevamente o verifica tu plan de facturación"
                    break

                # Buscar errores de timeout
                elif "timeout" in line.lower() or "timed out" in line.lower():
                    error_info["type"] = "Timeout Error"
                    error_info["message"] = "La operación tardó demasiado tiempo en completarse"
                    error_info["solution"] = "Intenta nuevamente o verifica la conectividad de red"
                    break

                # Buscar errores de navegación
                elif "navigation" in line.lower() and ("failed" in line.lower() or "error" in line.lower()):
                    error_info["type"] = "Navigation Error"
                    error_info["message"] = "Error al navegar a la página especificada"
                    error_info["solution"] = "Verifica que la URL sea correcta y accesible"
                    break

                # Buscar errores de elementos no encontrados
                elif "element" in line.lower() and ("not found" in line.lower() or "no encontrado" in line.lower()):
                    error_info["type"] = "Element Not Found"
                    error_info["message"] = "No se pudo encontrar el elemento especificado en la página"
                    error_info["solution"] = "Verifica que el elemento exista y sea visible en la página"
                    break

                # Buscar errores de credenciales
                elif "credentials" in line.lower() or "login" in line.lower() and "failed" in line.lower():
                    error_info["type"] = "Authentication Error"
                    error_info["message"] = "Error de autenticación o credenciales incorrectas"
                    error_info["solution"] = "Verifica que las credenciales sean correctas"
                    break

                # Buscar errores generales de browser-use
                elif "❌" in line or "ERROR" in line:
                    # Extraer el mensaje de error
                    if ":" in line:
                        parts = line.split(":", 1)
                        if len(parts) > 1:
                            error_info["type"] = "Execution Error"
                            error_info["message"] = parts[1].strip()
                            error_info["solution"] = "Revisa los pasos anteriores y verifica la configuración"
                            break

            # Si no se encontró información específica, proporcionar información genérica
            if not error_info:
                error_info = {
                    "type": "Unknown Error",
                    "message": "Se produjo un error durante la ejecución",
                    "solution": "Revisa los logs para más detalles o intenta nuevamente"
                }

        except Exception as e:
            logger.warning(f"Error extrayendo información de error de logs: {str(e)}")
            error_info = {
                "type": "Log Processing Error",
                "message": "No se pudo procesar la información de error",
                "solution": "Contacta al soporte técnico"
            }

        return error_info

    async def get_all_executions(self) -> List[Dict[str, Any]]:
        """Devuelve una lista de todas las ejecuciones."""
        return list(self.active_executions.values())

    def _convert_history_to_serializable(self, history) -> Dict[str, Any]:
        """Convierte AgentHistoryList a un formato JSON serializable."""
        try:
            if not history or not hasattr(history, 'history'):
                return {
                    "steps": [],
                    "summary": {},
                    "total_steps": 0
                }
            
            # Extraer información básica del historial
            serializable_history = {
                "steps": [],
                "summary": {},
                "total_steps": len(history.history) if history.history else 0
            }
            
            # Procesar cada paso del historial
            for i, step in enumerate(history.history):
                step_data = {
                    "step_number": i + 1,
                    "timestamp": None,
                    "action": None,
                    "result": None,
                    "model_output": None,
                    "error": None
                }
                
                try:
                    # Extraer información del modelo output
                    if hasattr(step, 'model_output') and step.model_output:
                        model_output = step.model_output
                        step_data["model_output"] = {
                            "thinking": getattr(model_output, 'thinking', None),
                            "current_state": {
                                "evaluation_previous_goal": getattr(model_output.current_state, 'evaluation_previous_goal', None) if hasattr(model_output, 'current_state') and model_output.current_state else None,
                                "memory": getattr(model_output.current_state, 'memory', None) if hasattr(model_output, 'current_state') and model_output.current_state else None,
                                "next_goal": getattr(model_output.current_state, 'next_goal', None) if hasattr(model_output, 'current_state') and model_output.current_state else None
                            } if hasattr(model_output, 'current_state') and model_output.current_state else None,
                            "action": str(model_output.action) if hasattr(model_output, 'action') and model_output.action else None
                        }
                    
                    # Extraer información del resultado
                    if hasattr(step, 'result') and step.result:
                        step_data["result"] = []
                        for result_item in step.result:
                            result_data = {
                                "extracted_content": getattr(result_item, 'extracted_content', None),
                                "error": getattr(result_item, 'error', None),
                                "success": getattr(result_item, 'success', None)
                            }
                            step_data["result"].append(result_data)
                    
                    # Timestamp si está disponible
                    if hasattr(step, 'timestamp'):
                        step_data["timestamp"] = str(step.timestamp) if step.timestamp else None
                        
                except Exception as e:
                    logger.warning(f"Error procesando step {i}: {str(e)}")
                    step_data["error"] = f"Error procesando step: {str(e)}"
                
                serializable_history["steps"].append(step_data)
            
            # Obtener resumen si está disponible
            try:
                if hasattr(history, 'get_summary'):
                    summary = history.get_summary()
                    if summary:
                        serializable_history["summary"] = {
                            "total_input_tokens": getattr(summary, 'total_input_tokens', 0),
                            "total_output_tokens": getattr(summary, 'total_output_tokens', 0),
                            "total_duration_seconds": getattr(summary, 'total_duration_seconds', 0),
                            "total_cost": getattr(summary, 'total_cost', 0.0)
                        }
            except Exception as e:
                logger.warning(f"Error obteniendo resumen del historial: {str(e)}")
                serializable_history["summary"] = {}
            
            return serializable_history
            
        except Exception as e:
            logger.error(f"Error convirtiendo historial a formato serializable: {str(e)}")
            return {
                "steps": [],
                "summary": {},
                "total_steps": 0,
                "error": f"Error converting history: {str(e)}"
            }
    
    def _transform_execution_to_standard_result(self, execution: Dict[str, Any]) -> Dict[str, Any]:
        """Transforma los datos de ejecución al formato StandardResult estándar."""
        try:
            logger.info(f"[TRANSFORM] Starting StandardResult transformation for execution {execution.get('execution_id')}")
            
            # Extraer el historial de browser-use
            history = execution.get("history")
            if not history:
                logger.warning(f"[TRANSFORM] No history found in execution")
                return self._transform_execution_basic_fallback(execution)
            
            logger.info(f"[TRANSFORM DEBUG] history type: {type(history)}")
            logger.info(f"[TRANSFORM DEBUG] history keys: {list(history.keys()) if isinstance(history, dict) else 'Not a dict'}")
            if isinstance(history, dict):
                logger.info(f"[TRANSFORM DEBUG] 'steps' in history: {'steps' in history}")
                logger.info(f"[TRANSFORM DEBUG] 'history' in history: {'history' in history}")
                logger.info(f"[TRANSFORM DEBUG] steps count: {len(history.get('steps', []))}")
                logger.info(f"[TRANSFORM DEBUG] history count: {len(history.get('history', []))}")
            
            # Preparar el contexto para el transformer
            context = {
                "execution_id": execution.get("execution_id"),
                "test_type": "codegen",
                "test_name": f"CodeGen Test - {execution.get('session_id', 'Unknown')}",
                "configuration": execution.get("browser_config", {}),
                "target_url": execution.get("target_url"),
                "current_iteration": 1,
                "execution_times": 1
            }
            
            # Si el history tiene un formato JSON serializado, crear un objeto mock
            if isinstance(history, dict) and "history" in history:
                # Crear un objeto mock que simule el historial de browser-use
                class MockHistory:
                    def __init__(self, history_data):
                        # Convert JSON step data to mock AgentHistory objects
                        self.history = []
                        steps = history_data.get("steps", [])
                        for step_data in steps:
                            mock_step = self._create_mock_agent_history(step_data)
                            self.history.append(mock_step)
                        self.summary = history_data.get("summary", {})
                        
                    def _create_mock_agent_history(self, step_data):
                        """Create a mock AgentHistory object from JSON step data"""
                        class MockAgentHistory:
                            def __init__(self, data):
                                self.model_output = self._create_mock_model_output(data.get("model_output"))
                                self.result = self._create_mock_results(data.get("result", []))
                                self.timestamp = data.get("timestamp")
                                
                            def _create_mock_model_output(self, model_output_data):
                                if not model_output_data:
                                    return None
                                class MockModelOutput:
                                    def __init__(self, data):
                                        self.thinking = data.get("thinking")
                                        self.current_state = data.get("current_state")
                                        self.action = data.get("action")
                                return MockModelOutput(model_output_data)
                                
                            def _create_mock_results(self, results_data):
                                mock_results = []
                                for result_data in results_data:
                                    class MockResult:
                                        def __init__(self, data):
                                            self.extracted_content = data.get("extracted_content")
                                            self.error = data.get("error")
                                            self.success = data.get("success")
                                    mock_results.append(MockResult(result_data))
                                return mock_results
                                
                        return MockAgentHistory(step_data)
                        
                    def final_result(self):
                        return f"Execution completed with {len(self.history)} steps"
                        
                    def get_summary(self):
                        return self.summary or {
                            "total_steps": len(self.history),
                            "is_successful": True,
                            "has_errors": False
                        }
                
                mock_history = MockHistory(history)
                # Usar el transformer estándar
                standard_result = transform_agent_history_to_standard_result(mock_history, **context)
            else:
                # Si ya es un objeto de browser-use, usarlo directamente
                standard_result = transform_agent_history_to_standard_result(history, **context)
            
            # Convertir StandardResult a dict para compatibilidad con la API
            result_dict = standard_result.model_dump() if hasattr(standard_result, 'model_dump') else dict(standard_result)
            
            # Agregar campos específicos del codegen
            result_dict.update({
                "execution_id": execution.get("execution_id"),
                "session_id": execution.get("session_id"),
                "target_url": execution.get("target_url"),
                "generated_code": execution.get("generated_code"),
                "browser_config": execution.get("browser_config", {}),
                "status": execution.get("status"),
                "error": execution.get("error")
            })
            
            # Asegurar que los timestamps estén en formato string
            for date_field in ["created_at", "updated_at", "completed_at"]:
                if date_field in execution:
                    if hasattr(execution[date_field], 'isoformat'):
                        result_dict[date_field] = execution[date_field].isoformat()
                    else:
                        result_dict[date_field] = execution[date_field]
            
            # Mantener start_time y end_time para compatibilidad con frontend
            result_dict["start_time"] = result_dict.get("created_at")
            result_dict["end_time"] = result_dict.get("completed_at") or result_dict.get("updated_at")
            
            logger.info(f"[TRANSFORM] StandardResult transformation complete: {len(result_dict.get('steps', []))} steps")
            logger.info(f"[TRANSFORM] Summary: {result_dict.get('summary', {})}")
            
            return result_dict
            
        except Exception as e:
            logger.error(f"[TRANSFORM] Error transforming to StandardResult: {e}")
            import traceback
            logger.error(f"[TRANSFORM] Traceback: {traceback.format_exc()}")
            
            # En caso de error, usar la transformación de fallback básica
            logger.warning(f"[TRANSFORM] Falling back to basic transformation")
            return self._transform_execution_basic_fallback(execution)

    def _transform_execution_basic_fallback(self, execution: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transformación básica de fallback cuando el StandardResult transformer falla.
        
        Proporciona una estructura mínima compatible con el frontend para evitar errores
        cuando el historial de browser-use no puede ser procesado correctamente.
        
        Args:
            execution: Datos raw de la ejecución
            
        Returns:
            Dict con estructura básica compatible con StandardResult
        """
        try:
            logger.info(f"[TRANSFORM FALLBACK] Applying basic transformation for execution {execution.get('execution_id')}")
            
            # Convertir datetime objects a strings para JSON serialization
            for date_field in ["created_at", "updated_at", "completed_at"]:
                if date_field in execution and hasattr(execution[date_field], 'isoformat'):
                    execution[date_field] = execution[date_field].isoformat()
            
            # Crear estructura básica compatible con StandardResult
            execution["steps"] = []
            execution["summary"] = {
                "total_steps": 0,
                "is_successful": execution.get("status") == "completed",
                "has_errors": execution.get("status") == "failed",
                "transformation_used": "basic_fallback"
            }
            execution["screenshots"] = execution.get("screenshots", [])
            execution["artifacts"] = execution.get("artifacts", [])
            
            # Mantener campos esenciales para compatibilidad
            execution["start_time"] = execution.get("created_at")
            execution["end_time"] = execution.get("completed_at") or execution.get("updated_at")
            
            logger.info(f"[TRANSFORM FALLBACK] Basic transformation completed")
            return execution
            
        except Exception as e:
            logger.error(f"[TRANSFORM FALLBACK] Error in basic fallback transformation: {e}")
            # Último recurso: devolver los datos sin procesar
            return execution
