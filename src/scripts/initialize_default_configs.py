#!/usr/bin/env python3
"""
Script para inicializar las configuraciones por defecto del sistema.
Este script debe ejecutarse al arrancar la aplicación para asegurar que
las configuraciones predefinidas estén disponibles como semillas modificables.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Añadir el directorio raíz al path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.services.default_configuration_service import DefaultConfigurationService
from src.database.connection import get_database

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def initialize_default_configurations():
    """
    Inicializa las configuraciones por defecto del sistema.
    """
    try:
        logger.info("Iniciando inicialización de configuraciones por defecto...")
        
        # Verificar conexión a la base de datos
        db = await get_database()
        logger.info("Conexión a MongoDB establecida")
        
        # Crear servicio de configuraciones por defecto
        default_service = DefaultConfigurationService()
        
        # Inicializar sistema
        result = await default_service.initialize_system_defaults()
        
        if result["success"]:
            logger.info("✅ Sistema de configuraciones inicializado exitosamente")
            
            seed_result = result["seed_result"]
            if seed_result["success"]:
                created_count = len([c for c in seed_result["seeded_configs"] if c["action"] == "created"])
                existing_count = len([c for c in seed_result["seeded_configs"] if c["action"] == "already_exists"])
                
                logger.info(f"📦 Configuraciones sembradas: {created_count} nuevas, {existing_count} existentes")
                
                for config in seed_result["seeded_configs"]:
                    status = "✨ Creada" if config["action"] == "created" else "✓ Ya existe"
                    logger.info(f"   {status}: {config['config_type']} (ID: {config['config_id']})")
            
            if result["missing_defaults"]:
                logger.warning(f"⚠️  Áreas sin configuración por defecto: {result['missing_defaults']}")
            else:
                logger.info("✅ Todas las áreas tienen configuraciones por defecto")
                
        else:
            logger.error(f"❌ Error inicializando sistema: {result['message']}")
            if "error" in result:
                logger.error(f"   Detalle: {result['error']}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Error crítico durante la inicialización: {str(e)}")
        return False

async def verify_system_health():
    """
    Verifica que el sistema esté funcionando correctamente después de la inicialización.
    """
    try:
        logger.info("Verificando salud del sistema...")
        
        default_service = DefaultConfigurationService()
        
        # Verificar que cada área tiene configuración por defecto
        areas = ["testing", "smoke", "exploration"]
        for area in areas:
            config = await default_service.get_default_configuration_for_area(area)
            if config:
                logger.info(f"✅ Área '{area}': Configuración por defecto disponible (ID: {config.id})")
            else:
                logger.warning(f"⚠️  Área '{area}': Sin configuración por defecto")
        
        logger.info("✅ Verificación de salud completada")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error verificando salud del sistema: {str(e)}")
        return False

async def main():
    """
    Función principal del script.
    """
    logger.info("🚀 Iniciando script de inicialización de configuraciones por defecto")
    
    # Inicializar configuraciones
    init_success = await initialize_default_configurations()
    if not init_success:
        logger.error("❌ Falló la inicialización. Abortando.")
        sys.exit(1)
    
    # Verificar salud del sistema
    health_success = await verify_system_health()
    if not health_success:
        logger.warning("⚠️  Verificación de salud falló, pero la inicialización fue exitosa")
    
    logger.info("🎉 Script de inicialización completado exitosamente")

if __name__ == "__main__":
    asyncio.run(main())