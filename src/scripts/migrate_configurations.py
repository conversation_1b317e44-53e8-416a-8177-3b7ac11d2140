#!/usr/bin/env python3
"""
Script de migración para transferir configuraciones de archivos JSON a MongoDB.

Este script:
1. <PERSON> las configuraciones existentes del directorio config/custom
2. Las migra a MongoDB usando el nuevo sistema
3. Mantiene un backup de las configuraciones originales
4. Proporciona un reporte detallado de la migración
"""

import asyncio
import json
import logging
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ConfigurationMigrator:
    """Migrador de configuraciones de JSON a MongoDB."""
    
    def __init__(self):
        self.config_dir = Path("config/custom")
        self.backup_dir = Path("config/backup_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        self.migration_report = {
            "started_at": datetime.now().isoformat(),
            "total_files": 0,
            "successful_migrations": 0,
            "failed_migrations": 0,
            "skipped_files": 0,
            "errors": [],
            "migrated_configs": []
        }
    
    async def run_migration(self) -> Dict[str, Any]:
        """Ejecuta la migración completa."""
        logger.info("Iniciando migración de configuraciones a MongoDB")
        
        try:
            # Verificar que existe el directorio de configuraciones
            if not self.config_dir.exists():
                logger.warning(f"Directorio de configuraciones no existe: {self.config_dir}")
                return self.migration_report
            
            # Crear backup
            await self._create_backup()
            
            # Obtener servicio de configuraciones
            from src.services.browser_configuration_service import BrowserConfigurationService
            config_service = BrowserConfigurationService()
            
            # Migrar configuraciones
            await self._migrate_configurations(config_service)
            
            # Finalizar reporte
            self.migration_report["completed_at"] = datetime.now().isoformat()
            
            logger.info("Migración completada")
            logger.info(f"Total archivos: {self.migration_report['total_files']}")
            logger.info(f"Migraciones exitosas: {self.migration_report['successful_migrations']}")
            logger.info(f"Migraciones fallidas: {self.migration_report['failed_migrations']}")
            logger.info(f"Archivos omitidos: {self.migration_report['skipped_files']}")
            
            return self.migration_report
            
        except Exception as e:
            logger.error(f"Error durante la migración: {str(e)}")
            self.migration_report["errors"].append({
                "type": "migration_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
            raise
    
    async def _create_backup(self):
        """Crea un backup de las configuraciones existentes."""
        try:
            if self.config_dir.exists() and any(self.config_dir.glob("*.json")):
                logger.info(f"Creando backup en: {self.backup_dir}")
                shutil.copytree(self.config_dir, self.backup_dir)
                logger.info("Backup creado exitosamente")
            else:
                logger.info("No hay configuraciones para respaldar")
        except Exception as e:
            logger.error(f"Error creando backup: {str(e)}")
            raise
    
    async def _migrate_configurations(self, config_service):
        """Migra todas las configuraciones JSON a MongoDB."""
        json_files = list(self.config_dir.glob("*.json"))
        self.migration_report["total_files"] = len(json_files)
        
        if not json_files:
            logger.info("No se encontraron archivos de configuración para migrar")
            return
        
        logger.info(f"Encontrados {len(json_files)} archivos de configuración")
        
        for config_file in json_files:
            await self._migrate_single_configuration(config_service, config_file)
    
    async def _migrate_single_configuration(self, config_service, config_file: Path):
        """Migra una configuración individual."""
        try:
            logger.info(f"Migrando: {config_file.name}")
            
            # Leer configuración JSON
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Validar estructura básica
            if not self._validate_config_structure(config_data):
                logger.warning(f"Estructura inválida en {config_file.name}, omitiendo")
                self.migration_report["skipped_files"] += 1
                return
            
            # Verificar si ya existe en MongoDB
            config_id = config_data.get("config_id")
            if config_id:
                existing = await config_service.get_configuration(config_id)
                if existing:
                    logger.info(f"Configuración {config_id} ya existe en MongoDB, omitiendo")
                    self.migration_report["skipped_files"] += 1
                    return
            
            # Preparar datos para MongoDB
            migration_data = self._prepare_migration_data(config_data)
            
            # Crear configuración en MongoDB
            config, warnings = await config_service.create_configuration(
                name=migration_data["name"],
                settings=migration_data["settings"],
                description=migration_data.get("description"),
                config_type=migration_data.get("config_type", "custom"),
                created_by="migration_script",
                tags=migration_data.get("tags", ["migrated"]),
                is_default=migration_data.get("is_default", False)
            )
            
            # Registrar migración exitosa
            self.migration_report["successful_migrations"] += 1
            self.migration_report["migrated_configs"].append({
                "original_file": config_file.name,
                "new_config_id": config.config_id,
                "name": config.name,
                "warnings": warnings,
                "migrated_at": datetime.now().isoformat()
            })
            
            logger.info(f"✓ Migrado exitosamente: {config.name} (ID: {config.config_id})")
            
            if warnings:
                logger.warning(f"Warnings para {config.name}: {warnings}")
            
        except Exception as e:
            logger.error(f"Error migrando {config_file.name}: {str(e)}")
            self.migration_report["failed_migrations"] += 1
            self.migration_report["errors"].append({
                "type": "file_migration_error",
                "file": config_file.name,
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def _validate_config_structure(self, config_data: Dict[str, Any]) -> bool:
        """Valida la estructura básica de una configuración."""
        required_fields = ["name", "settings"]
        
        for field in required_fields:
            if field not in config_data:
                return False
        
        # Validar que settings sea un diccionario
        if not isinstance(config_data["settings"], dict):
            return False
        
        return True
    
    def _prepare_migration_data(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara los datos para la migración a MongoDB."""
        # Aplicar defaults del entorno
        from src.api.config_routes import _apply_env_defaults
        
        migration_data = {
            "name": config_data["name"],
            "settings": _apply_env_defaults(config_data["settings"]),
            "description": config_data.get("description", f"Migrated from {config_data.get('config_id', 'unknown')}"),
            "config_type": config_data.get("config_type", "custom"),
            "tags": config_data.get("tags", []) + ["migrated"],
            "is_default": config_data.get("is_default", False)
        }
        
        # Limpiar configuraciones obsoletas
        obsolete_params = [
            "enable_memory", "memory_interval", "save_conversation_path",
            "memory_agent_id", "embedder_provider", "embedder_model", "embedder_dims"
        ]
        
        for param in obsolete_params:
            if param in migration_data["settings"]:
                logger.info(f"Removiendo parámetro obsoleto: {param}")
                del migration_data["settings"][param]
        
        return migration_data
    
    async def save_migration_report(self, output_file: str = "migration_report.json"):
        """Guarda el reporte de migración."""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.migration_report, f, indent=2, ensure_ascii=False)
            logger.info(f"Reporte de migración guardado en: {output_file}")
        except Exception as e:
            logger.error(f"Error guardando reporte: {str(e)}")


async def main():
    """Función principal del script de migración."""
    migrator = ConfigurationMigrator()
    
    try:
        # Ejecutar migración
        report = await migrator.run_migration()
        
        # Guardar reporte
        await migrator.save_migration_report()
        
        # Mostrar resumen
        print("\n" + "="*60)
        print("RESUMEN DE MIGRACIÓN")
        print("="*60)
        print(f"Total de archivos procesados: {report['total_files']}")
        print(f"Migraciones exitosas: {report['successful_migrations']}")
        print(f"Migraciones fallidas: {report['failed_migrations']}")
        print(f"Archivos omitidos: {report['skipped_files']}")
        
        if report['errors']:
            print(f"\nErrores encontrados: {len(report['errors'])}")
            for error in report['errors']:
                print(f"  - {error['type']}: {error['message']}")
        
        if report['migrated_configs']:
            print(f"\nConfiguraciones migradas:")
            for config in report['migrated_configs']:
                print(f"  - {config['name']} (ID: {config['new_config_id']})")
        
        print("\nMigración completada. Revisa el archivo 'migration_report.json' para más detalles.")
        
        return report
        
    except Exception as e:
        logger.error(f"Error fatal durante la migración: {str(e)}")
        await migrator.save_migration_report()
        raise


if __name__ == "__main__":
    # Ejecutar migración
    asyncio.run(main())