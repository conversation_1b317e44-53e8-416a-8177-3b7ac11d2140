"""Repository for browser configurations."""

from typing import Dict, Any, List, Optional
from datetime import datetime

from .base import BaseRepository
from ..models.browser_configuration import BrowserConfiguration, BrowserSessionPool


class BrowserConfigurationRepository(BaseRepository[BrowserConfiguration]):
    """Repository for browser configurations."""
    
    def __init__(self):
        super().__init__("browser_configurations")
    
    def to_document(self, model: BrowserConfiguration) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserConfiguration:
        """Convert MongoDB document to model."""
        return BrowserConfiguration(**document)
    
    def get_document_id(self, model: BrowserConfiguration) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def get_by_config_id(self, config_id: str) -> Optional[BrowserConfiguration]:
        """Get configuration by config_id."""
        return await BrowserConfiguration.find_one(BrowserConfiguration.config_id == config_id)
    
    async def get_by_project(self, project_id: str, include_global: bool = True) -> List[BrowserConfiguration]:
        """Get configurations for a specific project."""
        query = {
            "is_active": True,
            "$or": [
                {"project_id": project_id},
            ]
        }
        
        if include_global:
            query["$or"].append({"project_id": None})  # Global configurations
        
        return await BrowserConfiguration.find(query).to_list()
    
    async def get_by_suite(self, suite_id: str, include_project: bool = True, include_global: bool = True) -> List[BrowserConfiguration]:
        """Get configurations for a specific suite."""
        query = {
            "is_active": True,
            "$or": [
                {"suite_id": suite_id},
            ]
        }
        
        if include_project or include_global:
            # Get suite's project_id first
            suite_config = await BrowserConfiguration.find_one({"suite_id": suite_id})
            if suite_config and suite_config.project_id and include_project:
                query["$or"].append({"project_id": suite_config.project_id, "suite_id": None})
        
        if include_global:
            query["$or"].append({"project_id": None, "suite_id": None})  # Global configurations
        
        return await BrowserConfiguration.find(query).to_list()
    
    async def get_by_type(self, config_type: str) -> List[BrowserConfiguration]:
        """Get configurations by type."""
        return await BrowserConfiguration.find({
            "config_type": config_type,
            "is_active": True
        }).to_list()
    
    async def get_defaults(self) -> List[BrowserConfiguration]:
        """Get default configurations."""
        return await BrowserConfiguration.find({
            "is_default": True,
            "is_active": True
        }).to_list()
    
    async def get_popular(self, limit: int = 10) -> List[BrowserConfiguration]:
        """Get most popular configurations by usage count."""
        return await BrowserConfiguration.find({
            "is_active": True
        }).sort([("usage_count", -1)]).limit(limit).to_list()
    
    async def get_recent(self, limit: int = 10) -> List[BrowserConfiguration]:
        """Get most recently created configurations."""
        return await BrowserConfiguration.find({
            "is_active": True
        }).sort([("created_at", -1)]).limit(limit).to_list()
    
    async def search_by_tags(self, tags: List[str]) -> List[BrowserConfiguration]:
        """Search configurations by tags."""
        return await BrowserConfiguration.find({
            "tags": {"$in": tags},
            "is_active": True
        }).to_list()
    
    async def increment_usage(self, config_id: str) -> bool:
        """Increment usage count for a configuration."""
        config = await self.get_by_config_id(config_id)
        if config:
            config.increment_usage()
            await config.save()
            return True
        return False
    
    async def deactivate_config(self, config_id: str) -> bool:
        """Deactivate a configuration (soft delete)."""
        config = await self.get_by_config_id(config_id)
        if config:
            config.deactivate()
            await config.save()
            return True
        return False
    
    async def clone_config(self, config_id: str, new_name: str, created_by: Optional[str] = None) -> Optional[BrowserConfiguration]:
        """Clone an existing configuration with a new name."""
        original = await self.get_by_config_id(config_id)
        if not original:
            return None
        
        # Create new configuration based on original
        cloned = BrowserConfiguration(
            name=new_name,
            description=f"Cloned from {original.name}",
            config_type="custom",
            settings=original.settings.copy(),
            created_by=created_by,
            project_id=original.project_id,
            tags=original.tags.copy()
        )
        
        await cloned.save()
        return cloned


class BrowserSessionPoolRepository(BaseRepository[BrowserSessionPool]):
    """Repository for browser session pools."""
    
    def __init__(self):
        super().__init__("browser_session_pools")
    
    def to_document(self, model: BrowserSessionPool) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserSessionPool:
        """Convert MongoDB document to model."""
        return BrowserSessionPool(**document)
    
    def get_document_id(self, model: BrowserSessionPool) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def get_by_pool_id(self, pool_id: str) -> Optional[BrowserSessionPool]:
        """Get session pool by pool_id."""
        return await BrowserSessionPool.find_one(BrowserSessionPool.pool_id == pool_id)
    
    async def get_by_session_id(self, session_id: str) -> Optional[BrowserSessionPool]:
        """Get session pool by session_id."""
        return await BrowserSessionPool.find_one(BrowserSessionPool.session_id == session_id)
    
    async def get_by_suite(self, suite_id: str, active_only: bool = True) -> List[BrowserSessionPool]:
        """Get all session pools for a suite."""
        query = {"suite_id": suite_id}
        if active_only:
            query["is_active"] = True
        
        return await BrowserSessionPool.find(query).to_list()
    
    async def get_available_for_suite(self, suite_id: str) -> Optional[BrowserSessionPool]:
        """Get an available (unlocked) session for a suite."""
        return await BrowserSessionPool.find_one({
            "suite_id": suite_id,
            "is_active": True,
            "is_locked": False,
            "health_status": "healthy"
        })
    
    async def lock_session_for_test(self, suite_id: str, test_case_id: str) -> Optional[BrowserSessionPool]:
        """Lock an available session for a test case."""
        session = await self.get_available_for_suite(suite_id)
        if session and session.lock_for_test(test_case_id):
            await session.save()
            return session
        return None
    
    async def unlock_session(self, session_id: str) -> bool:
        """Unlock a session after test completion."""
        session = await self.get_by_session_id(session_id)
        if session:
            session.unlock()
            await session.save()
            return True
        return False
    
    async def update_session_health(self, session_id: str, status: str, error: Optional[str] = None) -> bool:
        """Update session health status."""
        session = await self.get_by_session_id(session_id)
        if session:
            session.update_health(status, error)
            await session.save()
            return True
        return False
    
    async def cleanup_expired_sessions(self) -> int:
        """Cleanup expired sessions."""
        now = datetime.utcnow()
        expired_sessions = await BrowserSessionPool.find({
            "expires_at": {"$lt": now},
            "is_active": True
        }).to_list()
        
        count = 0
        for session in expired_sessions:
            session.deactivate()
            await session.save()
            count += 1
        
        return count
    
    async def cleanup_unhealthy_sessions(self, max_errors: int = 5) -> int:
        """Cleanup sessions with too many errors."""
        unhealthy_sessions = await BrowserSessionPool.find({
            "error_count": {"$gte": max_errors},
            "is_active": True
        }).to_list()
        
        count = 0
        for session in unhealthy_sessions:
            session.deactivate()
            await session.save()
            count += 1
        
        return count
    
    async def get_session_stats(self, suite_id: str) -> Dict[str, Any]:
        """Get session statistics for a suite."""
        sessions = await self.get_by_suite(suite_id)
        
        total = len(sessions)
        active = len([s for s in sessions if s.is_active])
        locked = len([s for s in sessions if s.is_locked])
        healthy = len([s for s in sessions if s.health_status == "healthy"])
        
        return {
            "total_sessions": total,
            "active_sessions": active,
            "locked_sessions": locked,
            "healthy_sessions": healthy,
            "available_sessions": len([s for s in sessions if s.is_active and not s.is_locked and s.health_status == "healthy"]),
            "total_tests_executed": sum(s.test_count for s in sessions),
            "average_tests_per_session": sum(s.test_count for s in sessions) / max(total, 1)
        }