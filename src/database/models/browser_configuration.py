"""MongoDB model for browser configurations."""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from beanie import Document
from pydantic import Field
from enum import Enum


class ExecutionType(str, Enum):
    """Types of test execution that can use a configuration."""
    SMOKE = "smoke"
    FULL = "full"
    CASE = "case"
    SUITE = "suite"
    CODEGEN = "codegen"


class BrowserConfiguration(Document):
    """MongoDB document model for browser configurations."""
    
    # Unique identifiers
    config_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique configuration identifier")
    
    # Basic information
    name: str = Field(..., description="Configuration name")
    description: Optional[str] = Field(default=None, description="Configuration description")
    config_type: str = Field(default="custom", description="Configuration type (predefined, custom, suite)")
    
    # Configuration settings
    settings: Dict[str, Any] = Field(default_factory=dict, description="Browser configuration settings")
    
    # Execution compatibility
    execution_types: List[ExecutionType] = Field(
        default_factory=lambda: [ExecutionType.SMOKE, ExecutionType.FULL, ExecutionType.CASE, ExecutionType.SUITE, ExecutionType.CODEGEN],
        description="List of execution types that can use this configuration"
    )
    
    # Metadata
    is_default: bool = Field(default=False, description="Whether this is a default configuration")
    is_active: bool = Field(default=True, description="Whether this configuration is active")
    
    # Default configuration system
    is_predefined_seed: bool = Field(default=False, description="Whether this is a predefined seed configuration")
    is_system_default: bool = Field(default=False, description="Whether this is a system-wide default")
    is_project_default: bool = Field(default=False, description="Whether this is a project-specific default")
    category: Optional[str] = Field(default=None, description="Configuration category (testing, smoke, exploration, etc.)")
    
    # Ownership and sharing
    created_by: Optional[str] = Field(default=None, description="User ID who created this configuration")
    project_id: Optional[str] = Field(default=None, description="Project ID if configuration is project-specific")
    suite_id: Optional[str] = Field(default=None, description="Suite ID if configuration is suite-specific")
    
    # Validation
    validation_warnings: List[str] = Field(default_factory=list, description="Configuration validation warnings")
    is_valid: bool = Field(default=True, description="Whether configuration passed validation")
    
    # Usage tracking
    usage_count: int = Field(default=0, description="Number of times this configuration has been used")
    last_used_at: Optional[datetime] = Field(default=None, description="Last time this configuration was used")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    
    # Tags for organization
    tags: List[str] = Field(default_factory=list, description="Tags for categorizing configurations")
    
    class Settings:
        name = "browser_configurations"
        indexes = [
            "config_id",
            "config_type",
            "created_by",
            "project_id",
            "suite_id",
            "is_default",
            "is_active",
            "tags",
            [("created_at", -1)],  # Descending index for recent configurations
            [("usage_count", -1)],  # Descending index for popular configurations
            [("config_type", 1), ("is_active", 1)],  # Compound index for filtering
            [("project_id", 1), ("is_active", 1)],  # Project-specific configurations
            [("suite_id", 1), ("is_active", 1)],  # Suite-specific configurations
        ]
    
    def increment_usage(self) -> None:
        """Increment usage count and update last used timestamp."""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def add_tag(self, tag: str) -> None:
        """Add a tag if it doesn't already exist."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.utcnow()
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag if it exists."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.utcnow()
    
    def update_settings(self, new_settings: Dict[str, Any]) -> None:
        """Update configuration settings."""
        self.settings.update(new_settings)
        self.updated_at = datetime.utcnow()
    
    def deactivate(self) -> None:
        """Deactivate this configuration."""
        self.is_active = False
        self.updated_at = datetime.utcnow()
    
    def activate(self) -> None:
        """Activate this configuration."""
        self.is_active = True
        self.updated_at = datetime.utcnow()


class BrowserSessionPool(Document):
    """MongoDB document model for browser session pools used in test suites."""
    
    # Unique identifiers
    pool_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique pool identifier")
    session_id: str = Field(..., description="Browser session identifier")
    
    # Association
    suite_id: str = Field(..., description="Test suite ID using this session")
    project_id: str = Field(..., description="Project ID")
    execution_id: Optional[str] = Field(default=None, description="Current execution ID")
    
    # Configuration
    config_id: str = Field(..., description="Configuration ID used for this session")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Snapshot of configuration used")
    
    # Session state
    is_active: bool = Field(default=True, description="Whether session is active")
    is_locked: bool = Field(default=False, description="Whether session is locked by a test")
    locked_by: Optional[str] = Field(default=None, description="Test case ID that locked this session")
    locked_at: Optional[datetime] = Field(default=None, description="When session was locked")
    
    # Browser details
    browser_type: str = Field(default="chromium", description="Browser type (chromium, firefox, webkit)")
    browser_version: Optional[str] = Field(default=None, description="Browser version")
    user_data_dir: Optional[str] = Field(default=None, description="User data directory path")
    
    # Connection details
    cdp_url: Optional[str] = Field(default=None, description="Chrome DevTools Protocol URL")
    ws_endpoint: Optional[str] = Field(default=None, description="WebSocket endpoint")
    
    # Usage tracking
    test_count: int = Field(default=0, description="Number of tests executed in this session")
    last_test_at: Optional[datetime] = Field(default=None, description="Last test execution timestamp")
    
    # Health monitoring
    health_status: str = Field(default="healthy", description="Session health status")
    last_health_check: Optional[datetime] = Field(default=None, description="Last health check timestamp")
    error_count: int = Field(default=0, description="Number of errors encountered")
    last_error: Optional[str] = Field(default=None, description="Last error message")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(default=None, description="Last update timestamp")
    expires_at: Optional[datetime] = Field(default=None, description="Session expiration timestamp")
    
    class Settings:
        name = "browser_session_pools"
        indexes = [
            "pool_id",
            "session_id",
            "suite_id",
            "project_id",
            "execution_id",
            "config_id",
            "is_active",
            "is_locked",
            "locked_by",
            "health_status",
            [("created_at", -1)],
            [("last_test_at", -1)],
            [("suite_id", 1), ("is_active", 1)],
            [("project_id", 1), ("is_active", 1)],
            [("is_locked", 1), ("locked_by", 1)],
            [("health_status", 1), ("is_active", 1)],
        ]
    
    def lock_for_test(self, test_case_id: str) -> bool:
        """Lock session for a specific test case."""
        if self.is_locked:
            return False
        
        self.is_locked = True
        self.locked_by = test_case_id
        self.locked_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        return True
    
    def unlock(self) -> None:
        """Unlock session after test completion."""
        self.is_locked = False
        self.locked_by = None
        self.locked_at = None
        self.test_count += 1
        self.last_test_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def update_health(self, status: str, error: Optional[str] = None) -> None:
        """Update session health status."""
        self.health_status = status
        self.last_health_check = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        if error:
            self.error_count += 1
            self.last_error = error
    
    def deactivate(self) -> None:
        """Deactivate and cleanup session."""
        self.is_active = False
        self.is_locked = False
        self.locked_by = None
        self.updated_at = datetime.utcnow()