"""Browser Use Event Models for QAK MongoDB Implementation

Modelos de MongoDB para eventos de browser-use con correlación de IDs
y logging estructurado en formato JSON.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import Field, BaseModel
from beanie import Document
import uuid


class BrowserUseEventDocument(Document):
    """MongoDB document model para eventos de browser-use.
    
    Incluye correlación de IDs y metadatos para logging estructurado.
    """
    
    # Identificadores únicos
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID único del evento")
    correlation_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID de correlación para rastrear eventos relacionados")
    session_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la sesión")
    task_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la tarea")
    
    # Información del evento
    event_type: str = Field(..., description="Tipo de evento (session, task, step, output_file, update_task)")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp del evento")
    
    # Información del usuario y dispositivo
    user_id: Optional[str] = Field(default=None, description="ID del usuario")
    device_id: Optional[str] = Field(default=None, description="ID del dispositivo")
    
    # Datos del evento (estructura flexible)
    data: Dict[str, Any] = Field(default_factory=dict, description="Datos específicos del evento")
    
    # Metadatos para logging y análisis
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadatos adicionales")
    source_ip: Optional[str] = Field(default=None, description="IP de origen del evento")
    user_agent: Optional[str] = Field(default=None, description="User agent del cliente")
    
    # Campos de auditoría
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Fecha de creación")
    updated_at: Optional[datetime] = Field(default=None, description="Fecha de última actualización")
    
    # Configuración de MongoDB
    class Settings:
        name = "browser_use_events"
        indexes = [
            "event_type",
            "user_id",
            "device_id",
            "correlation_id",
            "session_correlation_id",
            "task_correlation_id",
            "timestamp",
            [("timestamp", -1)],  # Índice descendente para consultas recientes
            [("event_type", 1), ("timestamp", -1)],  # Índice compuesto
        ]


class BrowserUseSessionDocument(Document):
    """MongoDB document model para sesiones de browser-use."""
    
    session_id: str = Field(..., description="ID único de la sesión")
    correlation_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID de correlación")
    
    user_id: Optional[str] = Field(default=None, description="ID del usuario")
    device_id: Optional[str] = Field(default=None, description="ID del dispositivo")
    
    browser_session_id: str = Field(..., description="ID de la sesión del navegador")
    browser_session_live_url: str = Field(default="", description="URL en vivo de la sesión")
    browser_session_cdp_url: str = Field(default="", description="URL CDP de la sesión")
    browser_session_stopped: bool = Field(default=False, description="Si la sesión está detenida")
    browser_session_stopped_at: Optional[datetime] = Field(default=None, description="Timestamp de detención")
    
    browser_state: Dict[str, Any] = Field(default_factory=dict, description="Estado del navegador")
    browser_session_data: Optional[Dict[str, Any]] = Field(default=None, description="Datos de la sesión")
    
    # Metadatos
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadatos adicionales")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Fecha de creación")
    updated_at: Optional[datetime] = Field(default=None, description="Fecha de actualización")
    
    class Settings:
        name = "browser_use_sessions"
        indexes = [
            "session_id",
            "correlation_id",
            "user_id",
            "device_id",
            "browser_session_id",
            "created_at",
        ]


class BrowserUseTaskDocument(Document):
    """MongoDB document model para tareas de browser-use."""
    
    task_id: str = Field(..., description="ID único de la tarea")
    correlation_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID de correlación")
    session_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la sesión")
    
    agent_session_id: str = Field(..., description="ID de la sesión del agente")
    user_id: Optional[str] = Field(default=None, description="ID del usuario")
    device_id: Optional[str] = Field(default=None, description="ID del dispositivo")
    
    llm_model: str = Field(..., description="Modelo LLM utilizado")
    task: str = Field(..., description="Descripción de la tarea")
    
    stopped: bool = Field(default=False, description="Si la tarea está detenida")
    paused: bool = Field(default=False, description="Si la tarea está pausada")
    done_output: Optional[str] = Field(default=None, description="Salida de la tarea completada")
    
    scheduled_task_id: Optional[str] = Field(default=None, description="ID de tarea programada")
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp de inicio")
    finished_at: Optional[datetime] = Field(default=None, description="Timestamp de finalización")
    
    agent_state: Dict[str, Any] = Field(default_factory=dict, description="Estado del agente")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadatos adicionales")
    
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Fecha de creación")
    updated_at: Optional[datetime] = Field(default=None, description="Fecha de actualización")
    
    class Settings:
        name = "browser_use_tasks"
        indexes = [
            "task_id",
            "correlation_id",
            "session_correlation_id",
            "agent_session_id",
            "user_id",
            "device_id",
            "started_at",
            "finished_at",
        ]


class BrowserUseStepDocument(Document):
    """MongoDB document model para pasos de browser-use."""
    
    step_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID único del paso")
    correlation_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID de correlación")
    task_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la tarea")
    session_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la sesión")
    
    task_id: str = Field(..., description="ID de la tarea padre")
    user_id: Optional[str] = Field(default=None, description="ID del usuario")
    device_id: Optional[str] = Field(default=None, description="ID del dispositivo")
    
    step_number: int = Field(..., description="Número del paso en la secuencia")
    action_type: str = Field(..., description="Tipo de acción realizada")
    action_data: Dict[str, Any] = Field(default_factory=dict, description="Datos de la acción")
    
    result: Optional[Dict[str, Any]] = Field(default=None, description="Resultado del paso")
    error: Optional[str] = Field(default=None, description="Error si ocurrió")
    
    duration_ms: Optional[int] = Field(default=None, description="Duración en milisegundos")
    screenshot_url: Optional[str] = Field(default=None, description="URL de captura de pantalla")
    
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadatos adicionales")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Fecha de creación")
    
    class Settings:
        name = "browser_use_steps"
        indexes = [
            "step_id",
            "correlation_id",
            "task_correlation_id",
            "session_correlation_id",
            "task_id",
            "user_id",
            "device_id",
            "step_number",
            "created_at",
            [("task_id", 1), ("step_number", 1)],  # Índice compuesto para ordenar pasos
        ]


class BrowserUseFileDocument(Document):
    """MongoDB document model para archivos de salida de browser-use."""
    
    file_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID único del archivo")
    correlation_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="ID de correlación")
    task_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la tarea")
    session_correlation_id: Optional[str] = Field(default=None, description="ID de correlación de la sesión")
    
    task_id: str = Field(..., description="ID de la tarea padre")
    user_id: Optional[str] = Field(default=None, description="ID del usuario")
    device_id: Optional[str] = Field(default=None, description="ID del dispositivo")
    
    file_name: str = Field(..., description="Nombre del archivo")
    file_path: str = Field(..., description="Ruta del archivo")
    file_type: str = Field(..., description="Tipo de archivo")
    file_size: Optional[int] = Field(default=None, description="Tamaño del archivo en bytes")
    
    content_type: Optional[str] = Field(default=None, description="Tipo de contenido MIME")
    file_url: Optional[str] = Field(default=None, description="URL del archivo si está disponible")
    
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadatos adicionales")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Fecha de creación")
    
    class Settings:
        name = "browser_use_files"
        indexes = [
            "file_id",
            "correlation_id",
            "task_correlation_id",
            "session_correlation_id",
            "task_id",
            "user_id",
            "device_id",
            "file_name",
            "file_type",
            "created_at",
        ]


# Modelos Pydantic para requests/responses
class EventBatch(BaseModel):
    """Lote de eventos de browser-use."""
    events: List[Dict[str, Any]]


class EventStats(BaseModel):
    """Estadísticas de eventos."""
    total_events: int
    total_sessions: int
    total_tasks: int
    total_steps: int
    total_files: int
    event_types: Dict[str, int]
    unique_users: int
    unique_devices: int
    recent_events_count: int  # Últimos 5 minutos
    events_per_hour: float  # Promedio últimas 24 horas


class CorrelationInfo(BaseModel):
    """Información de correlación para eventos relacionados."""
    correlation_id: str
    session_correlation_id: Optional[str] = None
    task_correlation_id: Optional[str] = None
    related_events: List[str] = Field(default_factory=list)
    event_chain: List[Dict[str, Any]] = Field(default_factory=list)