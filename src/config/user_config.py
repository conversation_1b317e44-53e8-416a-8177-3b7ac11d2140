"""Configuración de usuario para QAK Browser-Use Events.

Este módulo proporciona funcionalidad para gestionar user IDs reales
en lugar del TEMP_USER_ID genérico de browser-use.
"""

import os
import json
import uuid
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import BaseModel


class UserProfile(BaseModel):
    """Perfil de usuario para eventos de browser-use."""
    
    user_id: str
    display_name: Optional[str] = None
    email: Optional[str] = None
    created_at: datetime
    last_used: Optional[datetime] = None
    metadata: Dict[str, Any] = {}


class UserConfig:
    """Gestión de configuración de usuario para browser-use."""
    
    def __init__(self):
        self.config_dir = Path.home() / '.qak'
        self.config_file = self.config_dir / 'user_config.json'
        self._ensure_config_dir()
    
    def _ensure_config_dir(self) -> None:
        """Asegurar que el directorio de configuración existe."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def get_user_id(self, request_context: Optional[Dict[str, Any]] = None) -> str:
        """Obtener user_id real usando múltiples fuentes.
        
        Prioridad:
        1. Variable de entorno QAK_USER_ID
        2. Archivo de configuración local
        3. Contexto de request (headers, IP, etc.)
        4. Generar uno único basado en el sistema
        
        Args:
            request_context: Contexto de la request con IP, headers, etc.
        
        Returns:
            str: User ID real o generado
        """
        
        # 1. Variable de entorno (máxima prioridad)
        env_user_id = os.getenv('QAK_USER_ID')
        if env_user_id and env_user_id.strip():
            self._update_last_used(env_user_id)
            return env_user_id.strip()
        
        # 2. Archivo de configuración
        config_user_id = self._get_user_from_config()
        if config_user_id:
            self._update_last_used(config_user_id)
            return config_user_id
        
        # 3. Contexto de request
        if request_context:
            context_user_id = self._extract_user_from_context(request_context)
            if context_user_id:
                return context_user_id
        
        # 4. Generar uno único para el sistema
        system_user_id = self._generate_system_user_id()
        self._save_user_profile(UserProfile(
            user_id=system_user_id,
            display_name=f"Usuario {system_user_id[:8]}",
            created_at=datetime.utcnow()
        ))
        return system_user_id
    
    def _get_user_from_config(self) -> Optional[str]:
        """Obtener user_id del archivo de configuración."""
        if not self.config_file.exists():
            return None
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('user_id')
        except (json.JSONDecodeError, IOError):
            return None
    
    def _extract_user_from_context(self, context: Dict[str, Any]) -> Optional[str]:
        """Extraer user_id del contexto de la request."""
        
        # Headers personalizados
        headers = context.get('headers', {})
        if isinstance(headers, dict):
            # X-User-ID header
            user_id = headers.get('x-user-id') or headers.get('X-User-ID')
            if user_id:
                return user_id
            
            # Authorization header (simplificado)
            auth = headers.get('authorization') or headers.get('Authorization')
            if auth and auth.startswith('Bearer '):
                # En un caso real, aquí decodificarías el JWT
                token = auth[7:]  # Remover 'Bearer '
                if len(token) > 10:  # Token válido básico
                    return f"auth_user_{hashlib.md5(token.encode()).hexdigest()[:8]}"
        
        # Query parameters
        query_params = context.get('query_params', {})
        if isinstance(query_params, dict):
            user_id = query_params.get('user_id')
            if user_id:
                return user_id
        
        # Fingerprinting básico (IP + User Agent)
        source_ip = context.get('source_ip', 'unknown')
        user_agent = context.get('user_agent', 'unknown')
        
        if source_ip != 'unknown' and user_agent != 'unknown':
            fingerprint = f"{source_ip}_{user_agent}"
            user_hash = hashlib.md5(fingerprint.encode()).hexdigest()[:8]
            return f"guest_{user_hash}"
        
        return None
    
    def _generate_system_user_id(self) -> str:
        """Generar un user_id único para el sistema."""
        
        # Intentar usar información del sistema
        try:
            import getpass
            system_user = getpass.getuser()
            if system_user:
                return f"sys_{system_user}"
        except Exception:
            pass
        
        # Fallback a UUID corto
        return f"user_{uuid.uuid4().hex[:8]}"
    
    def set_user_profile(self, user_id: str, display_name: Optional[str] = None, 
                        email: Optional[str] = None, **metadata) -> None:
        """Configurar perfil de usuario manualmente."""
        
        profile = UserProfile(
            user_id=user_id,
            display_name=display_name,
            email=email,
            created_at=datetime.utcnow(),
            metadata=metadata
        )
        
        self._save_user_profile(profile)
    
    def _save_user_profile(self, profile: UserProfile) -> None:
        """Guardar perfil de usuario en archivo de configuración."""
        
        try:
            config_data = profile.model_dump(mode='json')
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False, default=str)
            
            # Permisos restrictivos en sistemas Unix
            try:
                os.chmod(self.config_file, 0o600)
            except (OSError, AttributeError):
                pass  # Windows o sistema que no soporta chmod
                
        except IOError as e:
            print(f"Warning: No se pudo guardar la configuración de usuario: {e}")
    
    def _update_last_used(self, user_id: str) -> None:
        """Actualizar timestamp de último uso."""
        
        if not self.config_file.exists():
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if config.get('user_id') == user_id:
                config['last_used'] = datetime.utcnow().isoformat()
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
        
        except (json.JSONDecodeError, IOError):
            pass  # Ignorar errores de actualización
    
    def get_user_profile(self) -> Optional[UserProfile]:
        """Obtener perfil completo del usuario."""
        
        if not self.config_file.exists():
            return None
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return UserProfile(**config)
        except (json.JSONDecodeError, IOError, ValueError):
            return None
    
    def get_display_name(self, user_id: str) -> str:
        """Obtener nombre para mostrar del usuario."""
        
        profile = self.get_user_profile()
        if profile and profile.user_id == user_id and profile.display_name:
            return profile.display_name
        
        # Generar nombre legible basado en el user_id
        if user_id.startswith('sys_'):
            return f"Usuario del Sistema ({user_id[4:]})"
        elif user_id.startswith('guest_'):
            return f"Invitado ({user_id[6:]})"
        elif user_id.startswith('auth_user_'):
            return f"Usuario Autenticado ({user_id[10:]})"
        elif user_id.startswith('user_'):
            return f"Usuario ({user_id[5:]})"
        elif user_id == '99999999-9999-9999-9999-999999999999':
            return "Usuario Temporal"
        else:
            return f"Usuario ({user_id[:8]}...)"
    
    def clear_config(self) -> None:
        """Limpiar configuración de usuario."""
        
        if self.config_file.exists():
            try:
                self.config_file.unlink()
            except IOError:
                pass


# Instancia global para uso en toda la aplicación
user_config = UserConfig()


def get_real_user_id(request_context: Optional[Dict[str, Any]] = None) -> str:
    """Función de conveniencia para obtener user_id real.
    
    Args:
        request_context: Contexto de la request con headers, IP, etc.
    
    Returns:
        str: User ID real
    """
    return user_config.get_user_id(request_context)


def format_user_display_name(user_id: str) -> str:
    """Función de conveniencia para formatear nombre de usuario.
    
    Args:
        user_id: ID del usuario
    
    Returns:
        str: Nombre formateado para mostrar
    """
    return user_config.get_display_name(user_id)