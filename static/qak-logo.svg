<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 80" width="200" height="80">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="40" r="35" fill="url(#gradient)" opacity="0.1" stroke="url(#gradient)" stroke-width="2"/>
  
  <!-- QAK Text -->
  <text x="100" y="50" font-family="Arial, sans-serif" font-size="28" font-weight="bold" 
        text-anchor="middle" fill="url(#gradient)" filter="url(#glow)">
    QAK
  </text>
  
  <!-- Subtitle -->
  <text x="100" y="68" font-family="Arial, sans-serif" font-size="10" 
        text-anchor="middle" fill="#64748B" opacity="0.8">
    Quality Automation Kit
  </text>
</svg>