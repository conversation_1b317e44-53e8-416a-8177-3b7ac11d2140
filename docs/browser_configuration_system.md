# Sistema de Configuraciones de Browser y Sesiones para Test Suites

## Descripción General

Este documento describe el nuevo sistema de gestión de configuraciones de browser y sesiones para test suites, que permite:

1. **Persistir configuraciones personalizadas en MongoDB** en lugar de archivos JSON
2. **Configuraciones predefinidas modificables**: Las 4 configuraciones base (test_case, smoke, exploration, exploration_deep) se siembran como configuraciones modificables en MongoDB
3. **Sistema de configuraciones por defecto**: Permitir establecer configuraciones por defecto por área y proyecto
4. **Reutilizar sesiones de browser** entre tests de la misma suite
5. **Gestionar pools de sesiones** para optimizar el rendimiento
6. **Migrar configuraciones legacy** automáticamente

## Configuraciones Predefinidas

El sistema incluye 4 configuraciones optimizadas para Browser-Use v0.5.0:

### 1. Test Case / Test Suite
- **Propósito**: Ejecución de casos de prueba específicos y suites de testing
- **Características**: Visión habilitada, highlighting, timeouts optimizados, generación de GIFs
- **Uso**: Ideal para testing automatizado y validación de funcionalidades

### 2. Smoke
- **Propósito**: Pruebas rápidas de funcionalidad básica
- **Características**: Configuración ligera, timeouts reducidos, pasos limitados
- **Uso**: Verificación rápida de que la aplicación funciona básicamente

### 3. Exploration
- **Propósito**: Exploración general de aplicaciones web
- **Características**: Configuración balanceada, creatividad moderada
- **Uso**: Descubrimiento de funcionalidades y navegación exploratoria

### 4. Exploration Deep
- **Propósito**: Exploración exhaustiva y detallada
- **Características**: Máximos pasos, timeouts extendidos, alta creatividad
- **Uso**: Análisis profundo de aplicaciones complejas

## Sistema de Configuraciones Por Defecto

### Configuraciones Semilla (Seed Configurations)
Las 4 configuraciones predefinidas se siembran automáticamente en MongoDB al iniciar la aplicación como configuraciones **modificables**:

- Se crean con `is_predefined_seed: true` y `is_system_default: true`
- Son completamente editables por los usuarios
- Se categorizan por área: `testing`, `smoke`, `exploration`
- Solo se crean si no existen previamente

### Configuraciones Por Defecto por Área
Cada área del sistema puede tener configuraciones por defecto:

- **Sistema**: Configuración por defecto global para cada área
- **Proyecto**: Configuración por defecto específica por proyecto y área
- **Prioridad**: Proyecto > Sistema > Configuración semilla

### Gestión de Configuraciones Por Defecto
```python
# Obtener configuración por defecto para un área
config = await default_service.get_default_configuration_for_area("smoke", project_id="proj_123")

# Establecer configuración por defecto para proyecto
result = await default_service.set_project_default_configuration(
    project_id="proj_123",
    area="smoke", 
    config_id="config_456"
)

# Listar configuraciones disponibles para un área
configs = await default_service.get_area_configurations("testing", project_id="proj_123")
```

## Arquitectura del Sistema

### Modelos de Datos

#### BrowserConfiguration
```python
class BrowserConfiguration(Document):
    config_id: str          # ID único de la configuración
    name: str              # Nombre descriptivo
    description: str       # Descripción detallada
    config_type: str       # Tipo (predefined, custom, suite_specific)
    settings: Dict         # Configuración de BrowserHelperConfig
    created_by: str        # Usuario creador
    project_id: str        # ID del proyecto (opcional)
    suite_id: str          # ID de la suite (opcional)
    tags: List[str]        # Etiquetas para búsqueda
    is_default: bool       # Si es configuración por defecto
    is_active: bool        # Si está activa
    validation_warnings: List[str]  # Warnings de validación
    usage_count: int       # Contador de uso
    created_at: datetime   # Fecha de creación
    updated_at: datetime   # Fecha de actualización
```

#### BrowserSessionPool
```python
class BrowserSessionPool(Document):
    pool_id: str           # ID único del pool
    session_id: str        # ID de la sesión de browser
    suite_id: str          # ID de la test suite
    project_id: str        # ID del proyecto
    config_id: str         # ID de la configuración usada
    configuration: Dict    # Snapshot de la configuración
    browser_type: str      # Tipo de browser (chromium, firefox, webkit)
    status: str            # Estado (available, locked, expired)
    locked_by_test: str    # ID del test que tiene la sesión bloqueada
    health_status: str     # Estado de salud (healthy, unhealthy, error)
    user_data_dir: str     # Directorio de datos del usuario
    cdp_url: str           # URL del Chrome DevTools Protocol
    ws_endpoint: str       # WebSocket endpoint
    created_at: datetime   # Fecha de creación
    expires_at: datetime   # Fecha de expiración
    last_health_check: datetime  # Última verificación de salud
```

### Servicios

#### BrowserConfigurationService
Servicio principal que gestiona:
- Configuraciones de browser en MongoDB
- Pools de sesiones para test suites
- Migración de configuraciones legacy
- Validación y limpieza de datos

## API Endpoints

### Configuraciones en MongoDB

#### Crear Configuración
```http
POST /config/mongodb/configurations
```

#### Listar Configuraciones
```http
GET /config/mongodb/configurations?project_id=xxx&suite_id=yyy
```

#### Obtener Configuración
```http
GET /config/mongodb/configurations/{config_id}
```

#### Actualizar Configuración
```http
PUT /config/mongodb/configurations/{config_id}
```

#### Eliminar Configuración
```http
DELETE /config/mongodb/configurations/{config_id}
```

#### Clonar Configuración
```http
POST /config/mongodb/configurations/{config_id}/clone
```

### Configuraciones Por Defecto

#### Sembrar Configuraciones Predefinidas
```http
POST /config/defaults/seed
```

#### Inicializar Sistema de Configuraciones Por Defecto
```http
POST /config/defaults/initialize
```

#### Obtener Configuración Por Defecto para Área
```http
GET /config/defaults/area/{area}?project_id=xxx
```

#### Establecer Configuración Por Defecto para Proyecto
```http
POST /config/defaults/project/{project_id}/area/{area}
```

#### Obtener Configuraciones Disponibles para Área
```http
GET /config/defaults/area/{area}/configurations?project_id=xxx
```

#### Listar Áreas Disponibles
```http
GET /config/defaults/areas
```

### Sesiones de Browser para Test Suites

#### Obtener/Crear Sesión para Suite
```http
POST /config/sessions/suite/{suite_id}
```

#### Registrar Nueva Sesión
```http
POST /config/sessions/register
```

#### Bloquear Sesión para Test
```http
POST /config/sessions/{suite_id}/lock
```

#### Desbloquear Sesión
```http
POST /config/sessions/{session_id}/unlock
```

#### Actualizar Estado de Salud
```http
PUT /config/sessions/{session_id}/health
```

#### Listar Sesiones de Suite
```http
GET /config/sessions/suite/{suite_id}
```

#### Estadísticas de Sesiones
```http
GET /config/sessions/suite/{suite_id}/stats
```

#### Limpiar Sesiones Expiradas
```http
POST /config/sessions/cleanup
```

### Migración y Utilidades

#### Migrar Configuraciones Legacy
```http
POST /config/migrate/legacy
```

#### Estado de Salud del Sistema
```http
GET /config/health
```

## Flujo de Trabajo para Test Suites

### 1. Obtener Configuración para Suite
```python
# Opción A: Usar configuración por defecto del área
config_response = await get_default_configuration_for_area(
    area="testing",
    project_id="project_ecommerce"
)
config_id = config_response["config_id"]

# Opción B: Crear configuración específica para la suite
config_response = await create_mongodb_configuration(
    name="Config para Suite de Login",
    settings=test_case_settings,
    suite_id="suite_login_001",
    project_id="project_ecommerce"
)
config_id = config_response["config_id"]

# Opción C: Modificar configuración predefinida existente
configs = await get_area_configurations("testing", project_id="project_ecommerce")
test_case_config = next(c for c in configs if c["name"] == "test_case")

# Actualizar la configuración predefinida
updated_settings = {
    **test_case_config["settings"],
    "headless": False  # Modificar para mostrar el navegador
}

config_response = await update_mongodb_configuration(
    config_id=test_case_config["config_id"],
    settings=updated_settings
)
config_id = config_response["config_id"]
```

### 2. Inicialización de Sesión
```python
# 2. Obtener o crear sesión para la suite
session_response = await get_or_create_suite_session(
    suite_id="suite_login_001",
    project_id="project_ecommerce",
    config_id=config_id,
    browser_type="chromium"
)

if not session_response["session_found"]:
    # Crear nueva sesión de browser
    browser = await launch_browser(config)
    
    # Registrar sesión en el pool
    await register_browser_session(
        suite_id="suite_login_001",
        project_id="project_ecommerce",
        config_id=config_id,
        session_id=browser.session_id,
        cdp_url=browser.cdp_url,
        ws_endpoint=browser.ws_endpoint
    )
```

### 3. Ejecución de Tests
```python
# 3. Para cada test en la suite
for test_case in test_suite:
    # Bloquear sesión para el test
    session = await lock_session_for_test(
        suite_id="suite_login_001",
        test_case_id=test_case.id
    )
    
    try:
        # Ejecutar test usando la sesión
        result = await execute_test(test_case, session)
        
        # Actualizar estado de salud
        await update_session_health(
            session_id=session["session_id"],
            status="healthy"
        )
        
    except Exception as e:
        # Reportar error
        await update_session_health(
            session_id=session["session_id"],
            status="error",
            error=str(e)
        )
        
    finally:
        # Desbloquear sesión
        await unlock_session(session["session_id"])
```

### 4. Limpieza
```python
# 4. Al finalizar la suite, limpiar sesiones expiradas
cleanup_stats = await cleanup_sessions()
```

## Migración de Configuraciones Legacy

### Migración Automática
```bash
# Ejecutar script de migración
python src/scripts/migrate_configurations.py
```

### Migración vía API
```http
POST /config/migrate/legacy?backup_existing=true
```

### Características de la Migración
- **Backup automático** de configuraciones existentes
- **Validación** de estructura y configuraciones
- **Limpieza** de parámetros obsoletos
- **Reporte detallado** de la migración
- **Manejo de errores** robusto

## Beneficios del Nuevo Sistema

### 1. Configuraciones Predefinidas Modificables
- **Flexibilidad**: Las 4 configuraciones base (test_case, smoke, exploration, exploration_deep) son ahora editables
- **Persistencia**: Las configuraciones se mantienen entre reinicios del servidor
- **Inicialización automática**: Las configuraciones semilla se crean automáticamente al iniciar la aplicación
- **Sistema de configuraciones por defecto**: Flexibilidad para establecer configuraciones por defecto por área y proyecto

### 2. Reutilización de Sesiones
- **Mejor rendimiento**: No reiniciar browser para cada test
- **Estado persistente**: Mantener cookies, localStorage, etc.
- **Recursos optimizados**: Menor uso de memoria y CPU

### 3. Configuraciones Persistentes
- **Centralización**: Todas las configuraciones en MongoDB
- **Versionado**: Historial de cambios
- **Compartición**: Entre proyectos y equipos
- **Búsqueda**: Por tags, proyecto, suite
- **Migración automática**: Transición suave desde el sistema de archivos JSON

### 4. Gestión Avanzada
- **Monitoreo**: Estado de salud de sesiones
- **Estadísticas**: Uso y rendimiento
- **Limpieza automática**: Sesiones expiradas
- **Escalabilidad**: Soporte para múltiples suites concurrentes

## Consideraciones de Implementación

### Seguridad
- Las configuraciones pueden contener información sensible
- Implementar autenticación y autorización adecuadas
- Validar todas las entradas de usuario

### Rendimiento
- Monitorear el uso de memoria de sesiones persistentes
- Implementar límites en el número de sesiones concurrentes
- Limpiar sesiones expiradas regularmente

### Compatibilidad
- Mantener compatibilidad con configuraciones legacy
- Proporcionar migración gradual
- Documentar cambios en la API

## Próximos Pasos

1. **Implementar en el frontend** la gestión de configuraciones MongoDB
2. **Integrar con el orquestador** de ejecuciones para usar sesiones persistentes
3. **Agregar métricas** y monitoreo avanzado
4. **Implementar notificaciones** para sesiones con problemas
5. **Crear dashboard** para gestión de sesiones y configuraciones