# Configuraciones por Tipo de Ejecución

Este documento explica cómo asociar configuraciones específicas de navegador a diferentes tipos de ejecución en QAK.

## Descripción General

El sistema ahora permite especificar qué tipos de ejecución pueden usar cada configuración de navegador. Esto proporciona mayor flexibilidad y control sobre qué configuraciones están disponibles para cada tipo de test.

## Tipos de Ejecución Soportados

- `smoke`: Tests de humo rápidos
- `full`: Tests completos con escenarios Gherkin
- `case`: Ejecución de casos de prueba individuales
- `suite`: Ejecución de suites de pruebas
- `codegen`: Generación de código de automatización

## Configuración del Campo execution_types

### Valores por Defecto

Cuando se crea una nueva configuración sin especificar `execution_types`, se asignan todos los tipos por defecto:

```json
{
  "execution_types": ["smoke", "full", "case", "suite", "codegen"]
}
```

### Configuración Personalizada

Puedes especificar tipos específicos al crear o actualizar una configuración:

```json
{
  "name": "testMuyBuenos",
  "description": "Configuración optimizada para casos de prueba y smoke tests",
  "config_type": "custom",
  "execution_types": ["case", "smoke"],
  "settings": {
    "browser_type": "chromium",
    "headless": false,
    "viewport": {"width": 1920, "height": 1080}
  }
}
```

## API Endpoints

### 1. Crear Configuración con Tipos de Ejecución

```http
POST /config/mongodb/configurations
Content-Type: application/json

{
  "name": "ConfiguracionRapida",
  "description": "Solo para smoke tests",
  "config_type": "custom",
  "execution_types": ["smoke"],
  "settings": {
    "browser_type": "chromium",
    "headless": true,
    "timeout": 5000
  }
}
```

### 2. Obtener Configuraciones por Tipo de Ejecución

```http
GET /config/mongodb/configurations/execution-type/smoke
```

Parámetros de consulta opcionales:
- `project_id`: Filtrar por proyecto
- `suite_id`: Filtrar por suite
- `include_inactive`: Incluir configuraciones inactivas

### 3. Actualizar Tipos de Ejecución de una Configuración

```http
PATCH /config/mongodb/configurations/{config_id}/execution-types
Content-Type: application/json

["case", "suite"]
```

### 4. Actualizar Configuración Completa

```http
PUT /config/mongodb/configurations/{config_id}
Content-Type: application/json

{
  "name": "ConfiguracionActualizada",
  "execution_types": ["full", "codegen"],
  "settings": {
    "browser_type": "firefox",
    "headless": false
  }
}
```

## Ejemplos de Uso

### Ejemplo 1: Configuración Solo para Smoke Tests

```bash
curl -X POST "http://localhost:8000/config/mongodb/configurations" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "SmokeOnly",
    "description": "Configuración rápida para smoke tests",
    "config_type": "performance",
    "execution_types": ["smoke"],
    "settings": {
      "browser_type": "chromium",
      "headless": true,
      "timeout": 3000,
      "viewport": {"width": 1280, "height": 720}
    }
  }'
```

### Ejemplo 2: Configuración para Cases y Suites

```bash
curl -X POST "http://localhost:8000/config/mongodb/configurations" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "testMuyBuenos",
    "description": "Configuración optimizada para casos y suites",
    "config_type": "custom",
    "execution_types": ["case", "suite"],
    "settings": {
      "browser_type": "chromium",
      "headless": false,
      "timeout": 10000,
      "viewport": {"width": 1920, "height": 1080},
      "slow_mo": 100
    }
  }'
```

### Ejemplo 3: Obtener Configuraciones para un Tipo Específico

```bash
# Obtener todas las configuraciones que soportan ejecución de casos
curl "http://localhost:8000/config/mongodb/configurations/execution-type/case"

# Obtener configuraciones de smoke para un proyecto específico
curl "http://localhost:8000/config/mongodb/configurations/execution-type/smoke?project_id=mi-proyecto"
```

## Migración de Configuraciones Existentes

Para migrar configuraciones existentes y agregar el campo `execution_types`, ejecuta el script de migración:

```bash
python scripts/migrate_execution_types.py
```

Este script:
1. Encuentra todas las configuraciones sin el campo `execution_types`
2. Les asigna todos los tipos de ejecución por defecto
3. Actualiza el timestamp `updated_at`
4. Verifica que la migración se completó correctamente

## Validaciones

### Tipos de Ejecución Válidos

Solo se aceptan los siguientes valores:
- `smoke`
- `full`
- `case`
- `suite`
- `codegen`

### Reglas de Validación

1. **Al menos un tipo**: Toda configuración debe tener al menos un tipo de ejecución
2. **Tipos válidos**: Solo se aceptan los tipos definidos en el enum `ExecutionType`
3. **Normalización**: Los tipos se normalizan a minúsculas automáticamente
4. **Valores por defecto**: Si no se especifican tipos válidos, se asignan todos por defecto

## Casos de Uso Comunes

### 1. Configuraciones Especializadas

```json
{
  "name": "ConfiguracionRapida",
  "execution_types": ["smoke"],
  "settings": {
    "headless": true,
    "timeout": 3000
  }
}
```

### 2. Configuraciones de Desarrollo

```json
{
  "name": "ConfiguracionDebug",
  "execution_types": ["case", "codegen"],
  "settings": {
    "headless": false,
    "slow_mo": 500,
    "devtools": true
  }
}
```

### 3. Configuraciones de Producción

```json
{
  "name": "ConfiguracionProduccion",
  "execution_types": ["full", "suite"],
  "settings": {
    "headless": true,
    "timeout": 30000,
    "retries": 3
  }
}
```

## Respuesta de la API

Todas las respuestas de configuraciones ahora incluyen el campo `execution_types`:

```json
{
  "config_id": "config_123",
  "name": "testMuyBuenos",
  "description": "Configuración personalizada",
  "config_type": "custom",
  "execution_types": ["case", "smoke"],
  "settings": {...},
  "is_valid": true,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

## Consideraciones de Rendimiento

1. **Filtrado eficiente**: Las consultas por tipo de ejecución utilizan índices de MongoDB
2. **Caché**: Las configuraciones se cachean para mejorar el rendimiento
3. **Validación**: La validación de tipos se realiza una sola vez al crear/actualizar

## Troubleshooting

### Error: "Tipo de ejecución inválido"

**Causa**: Se especificó un tipo de ejecución no válido

**Solución**: Usar solo los tipos válidos: `smoke`, `full`, `case`, `suite`, `codegen`

### Error: "Debe especificar al menos un tipo de ejecución"

**Causa**: Se envió una lista vacía de tipos de ejecución

**Solución**: Especificar al menos un tipo válido

### No se encuentran configuraciones para un tipo

**Causa**: No hay configuraciones que soporten ese tipo de ejecución

**Solución**: 
1. Crear una nueva configuración con el tipo deseado
2. Actualizar una configuración existente para incluir el tipo
3. Verificar que las configuraciones estén activas (`is_active: true`)