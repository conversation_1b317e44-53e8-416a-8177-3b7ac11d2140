# Plan 3: Sistema de Versionado de Configuraciones

## Resumen Ejecutivo

**Objetivo**: Implementar un sistema robusto de versionado y gestión de configuraciones que permita el control de cambios, rollbacks seguros y evolución controlada del sistema de testing.

**Factibilidad**: ✅ **ALTA** - Integración natural con la arquitectura configurable existente.

## Casos de Uso

1. **Control de Versiones**: Seguimiento de cambios en configuraciones
2. **Rollback Seguro**: Reversión rápida a configuraciones estables
3. **Gestión de Entornos**: Configuraciones específicas por entorno
4. **Auditoría de Cambios**: Trazabilidad completa de modificaciones
5. **Distribución Controlada**: Despliegue gradual de nuevas configuraciones

## Arquitectura del Sistema

### 1. Gestor de Versiones de Configuración

```python
class ConfigurationVersionManager:
    """Gestor principal de versiones de configuración"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.version_store = VersionStore(self.config['storage'])
        self.change_tracker = ChangeTracker()
        self.validator = ConfigurationValidator()
        self.deployment_manager = DeploymentManager()
        
    async def create_version(self, 
                           config_data: Dict, 
                           version_info: VersionInfo,
                           validate: bool = True) -> ConfigVersion:
        """Crear nueva versión de configuración"""
        
        # Validar configuración si se solicita
        if validate:
            validation_result = await self.validator.validate(config_data)
            if not validation_result.is_valid:
                raise ConfigurationValidationError(validation_result.errors)
        
        # Generar hash de contenido
        content_hash = self._generate_content_hash(config_data)
        
        # Verificar si ya existe esta configuración
        existing_version = await self.version_store.find_by_hash(content_hash)
        if existing_version:
            return existing_version
        
        # Crear nueva versión
        version = ConfigVersion(
            id=str(uuid.uuid4()),
            version_number=await self._generate_version_number(version_info),
            content_hash=content_hash,
            config_data=config_data,
            metadata=version_info,
            created_at=datetime.utcnow(),
            created_by=version_info.author,
            parent_version=version_info.parent_version,
            tags=version_info.tags or [],
            environment=version_info.environment
        )
        
        # Almacenar versión
        await self.version_store.store(version)
        
        # Registrar cambio
        await self.change_tracker.track_change(
            ChangeEvent(
                type='version_created',
                version_id=version.id,
                timestamp=datetime.utcnow(),
                author=version_info.author,
                description=version_info.description
            )
        )
        
        return version
    
    async def get_version(self, version_id: str) -> ConfigVersion:
        """Obtener versión específica"""
        version = await self.version_store.get(version_id)
        if not version:
            raise VersionNotFoundError(f"Versión {version_id} no encontrada")
        return version
    
    async def list_versions(self, 
                          filters: Optional[Dict] = None,
                          limit: int = 50) -> List[ConfigVersion]:
        """Listar versiones con filtros opcionales"""
        return await self.version_store.list(filters, limit)
    
    async def compare_versions(self, 
                             version_a: str, 
                             version_b: str) -> VersionComparison:
        """Comparar dos versiones de configuración"""
        v_a = await self.get_version(version_a)
        v_b = await self.get_version(version_b)
        
        return VersionComparison(
            version_a=v_a,
            version_b=v_b,
            differences=self._calculate_differences(v_a.config_data, v_b.config_data),
            compatibility=await self._check_compatibility(v_a, v_b),
            migration_path=await self._generate_migration_path(v_a, v_b)
        )
    
    async def create_branch(self, 
                          base_version: str, 
                          branch_name: str,
                          branch_info: BranchInfo) -> ConfigBranch:
        """Crear rama de configuración para desarrollo paralelo"""
        base = await self.get_version(base_version)
        
        branch = ConfigBranch(
            id=str(uuid.uuid4()),
            name=branch_name,
            base_version=base_version,
            created_at=datetime.utcnow(),
            created_by=branch_info.author,
            description=branch_info.description,
            environment=branch_info.environment,
            status='active'
        )
        
        await self.version_store.create_branch(branch)
        return branch
    
    async def merge_branch(self, 
                         branch_name: str, 
                         target_version: str,
                         merge_strategy: str = 'auto') -> ConfigVersion:
        """Fusionar rama con versión objetivo"""
        branch = await self.version_store.get_branch(branch_name)
        target = await self.get_version(target_version)
        
        # Obtener cambios de la rama
        branch_changes = await self.version_store.get_branch_changes(branch_name)
        
        # Aplicar estrategia de fusión
        merged_config = await self._apply_merge_strategy(
            target.config_data, 
            branch_changes, 
            merge_strategy
        )
        
        # Crear nueva versión con cambios fusionados
        merge_info = VersionInfo(
            author=branch.created_by,
            description=f"Merge branch '{branch_name}' into {target.version_number}",
            parent_version=target_version,
            tags=['merge'],
            environment=target.environment
        )
        
        return await self.create_version(merged_config, merge_info)
    
    def _generate_content_hash(self, config_data: Dict) -> str:
        """Generar hash único del contenido de configuración"""
        import hashlib
        import json
        
        # Normalizar datos para hash consistente
        normalized = json.dumps(config_data, sort_keys=True, separators=(',', ':'))
        return hashlib.sha256(normalized.encode()).hexdigest()
    
    async def _generate_version_number(self, version_info: VersionInfo) -> str:
        """Generar número de versión semántico"""
        if version_info.version_number:
            return version_info.version_number
        
        # Obtener última versión
        latest = await self.version_store.get_latest_version(version_info.environment)
        
        if not latest:
            return "1.0.0"
        
        # Incrementar basado en tipo de cambio
        major, minor, patch = map(int, latest.version_number.split('.'))
        
        if version_info.change_type == 'breaking':
            return f"{major + 1}.0.0"
        elif version_info.change_type == 'feature':
            return f"{major}.{minor + 1}.0"
        else:  # patch
            return f"{major}.{minor}.{patch + 1}"

class ConfigVersion:
    """Representación de una versión de configuración"""
    
    def __init__(self, **kwargs):
        self.id = kwargs['id']
        self.version_number = kwargs['version_number']
        self.content_hash = kwargs['content_hash']
        self.config_data = kwargs['config_data']
        self.metadata = kwargs['metadata']
        self.created_at = kwargs['created_at']
        self.created_by = kwargs['created_by']
        self.parent_version = kwargs.get('parent_version')
        self.tags = kwargs.get('tags', [])
        self.environment = kwargs['environment']
        self.status = kwargs.get('status', 'active')
        self.deployment_history = kwargs.get('deployment_history', [])
    
    def to_dict(self) -> Dict:
        """Convertir a diccionario para serialización"""
        return {
            'id': self.id,
            'version_number': self.version_number,
            'content_hash': self.content_hash,
            'config_data': self.config_data,
            'metadata': self.metadata.__dict__ if hasattr(self.metadata, '__dict__') else self.metadata,
            'created_at': self.created_at.isoformat(),
            'created_by': self.created_by,
            'parent_version': self.parent_version,
            'tags': self.tags,
            'environment': self.environment,
            'status': self.status,
            'deployment_history': self.deployment_history
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ConfigVersion':
        """Crear instancia desde diccionario"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)
```

### 2. Sistema de Validación de Configuraciones

```python
class ConfigurationValidator:
    """Validador de configuraciones con reglas extensibles"""
    
    def __init__(self, rules_path: Optional[str] = None):
        self.validation_rules = self._load_validation_rules(rules_path)
        self.schema_validator = SchemaValidator()
        self.dependency_checker = DependencyChecker()
        
    async def validate(self, config_data: Dict) -> ValidationResult:
        """Validar configuración completa"""
        errors = []
        warnings = []
        
        # Validación de esquema
        schema_result = await self.schema_validator.validate(config_data)
        errors.extend(schema_result.errors)
        warnings.extend(schema_result.warnings)
        
        # Validación de reglas de negocio
        business_result = await self._validate_business_rules(config_data)
        errors.extend(business_result.errors)
        warnings.extend(business_result.warnings)
        
        # Validación de dependencias
        dependency_result = await self.dependency_checker.check(config_data)
        errors.extend(dependency_result.errors)
        warnings.extend(dependency_result.warnings)
        
        # Validación de compatibilidad
        compatibility_result = await self._validate_compatibility(config_data)
        errors.extend(compatibility_result.errors)
        warnings.extend(compatibility_result.warnings)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            score=self._calculate_quality_score(errors, warnings)
        )
    
    def _load_validation_rules(self, rules_path: Optional[str]) -> List[ValidationRule]:
        """Cargar reglas de validación"""
        default_rules = [
            ValidationRule(
                name="required_fields",
                description="Verificar campos requeridos",
                validator=self._validate_required_fields
            ),
            ValidationRule(
                name="value_ranges",
                description="Verificar rangos de valores",
                validator=self._validate_value_ranges
            ),
            ValidationRule(
                name="pattern_consistency",
                description="Verificar consistencia de patrones",
                validator=self._validate_pattern_consistency
            ),
            ValidationRule(
                name="security_settings",
                description="Verificar configuraciones de seguridad",
                validator=self._validate_security_settings
            )
        ]
        
        if rules_path:
            custom_rules = self._load_custom_rules(rules_path)
            default_rules.extend(custom_rules)
        
        return default_rules
    
    async def _validate_required_fields(self, config: Dict) -> ValidationResult:
        """Validar campos requeridos"""
        errors = []
        required_sections = ['patterns', 'testing_modes', 'bug_detection']
        
        for section in required_sections:
            if section not in config:
                errors.append(f"Sección requerida '{section}' no encontrada")
            elif not config[section]:
                errors.append(f"Sección '{section}' está vacía")
        
        # Validar campos específicos por sección
        if 'patterns' in config:
            for pattern_name, pattern_config in config['patterns'].items():
                if 'class' not in pattern_config:
                    errors.append(f"Patrón '{pattern_name}' no tiene clase definida")
                if 'selectors' not in pattern_config:
                    errors.append(f"Patrón '{pattern_name}' no tiene selectores definidos")
        
        return ValidationResult(is_valid=len(errors) == 0, errors=errors)
    
    async def _validate_value_ranges(self, config: Dict) -> ValidationResult:
        """Validar rangos de valores"""
        errors = []
        warnings = []
        
        # Validar timeouts
        if 'timeouts' in config:
            timeouts = config['timeouts']
            if timeouts.get('page_load', 0) > 60000:
                warnings.append("Timeout de carga de página muy alto (>60s)")
            if timeouts.get('element_wait', 0) < 1000:
                warnings.append("Timeout de espera de elemento muy bajo (<1s)")
        
        # Validar límites de recursos
        if 'resources' in config:
            resources = config['resources']
            if resources.get('max_memory_mb', 0) > 8192:
                warnings.append("Límite de memoria muy alto (>8GB)")
            if resources.get('max_cpu_percent', 0) > 90:
                warnings.append("Límite de CPU muy alto (>90%)")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    async def _validate_security_settings(self, config: Dict) -> ValidationResult:
        """Validar configuraciones de seguridad"""
        errors = []
        warnings = []
        
        # Verificar configuraciones de seguridad
        if 'security' in config:
            security = config['security']
            
            # Verificar que las credenciales no estén hardcodeadas
            if 'credentials' in security:
                for cred_key, cred_value in security['credentials'].items():
                    if isinstance(cred_value, str) and len(cred_value) > 0:
                        if not cred_value.startswith('${') or not cred_value.endswith('}'):
                            errors.append(f"Credencial '{cred_key}' parece estar hardcodeada")
            
            # Verificar configuraciones SSL
            if security.get('verify_ssl', True) is False:
                warnings.append("Verificación SSL deshabilitada - riesgo de seguridad")
            
            # Verificar configuraciones de proxy
            if 'proxy' in security and security['proxy'].get('log_requests', False):
                warnings.append("Logging de requests habilitado - puede exponer datos sensibles")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
```

### 3. Gestor de Despliegue de Configuraciones

```python
class DeploymentManager:
    """Gestor de despliegue de configuraciones"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.deployment_strategies = self._setup_deployment_strategies()
        self.rollback_manager = RollbackManager()
        self.health_checker = HealthChecker()
        
    async def deploy_version(self, 
                           version_id: str,
                           environment: str,
                           strategy: str = 'blue_green') -> DeploymentResult:
        """Desplegar versión de configuración"""
        
        # Obtener versión a desplegar
        version = await self.version_manager.get_version(version_id)
        
        # Validar que la versión sea compatible con el entorno
        compatibility = await self._check_environment_compatibility(version, environment)
        if not compatibility.is_compatible:
            raise IncompatibleVersionError(compatibility.issues)
        
        # Obtener estrategia de despliegue
        deployment_strategy = self.deployment_strategies[strategy]
        
        # Crear plan de despliegue
        deployment_plan = await deployment_strategy.create_plan(
            version=version,
            environment=environment,
            current_config=await self._get_current_config(environment)
        )
        
        # Ejecutar despliegue
        deployment_result = await self._execute_deployment(deployment_plan)
        
        # Verificar salud del sistema después del despliegue
        health_check = await self.health_checker.check_system_health(environment)
        
        if not health_check.is_healthy:
            # Rollback automático si el sistema no está saludable
            await self.rollback_manager.rollback_to_previous(environment)
            raise DeploymentFailedError("Sistema no saludable después del despliegue")
        
        # Registrar despliegue exitoso
        await self._record_deployment(version_id, environment, deployment_result)
        
        return deployment_result
    
    async def _execute_deployment(self, plan: DeploymentPlan) -> DeploymentResult:
        """Ejecutar plan de despliegue"""
        result = DeploymentResult(
            plan_id=plan.id,
            started_at=datetime.utcnow(),
            steps_completed=[],
            status='in_progress'
        )
        
        try:
            for step in plan.steps:
                step_result = await self._execute_deployment_step(step)
                result.steps_completed.append(step_result)
                
                # Verificar si el paso falló
                if not step_result.success:
                    result.status = 'failed'
                    result.error = step_result.error
                    break
            
            if result.status == 'in_progress':
                result.status = 'completed'
                result.completed_at = datetime.utcnow()
        
        except Exception as e:
            result.status = 'failed'
            result.error = str(e)
            result.completed_at = datetime.utcnow()
        
        return result
    
    async def _execute_deployment_step(self, step: DeploymentStep) -> StepResult:
        """Ejecutar paso individual de despliegue"""
        try:
            if step.type == 'backup_current':
                await self._backup_current_config(step.environment)
            elif step.type == 'validate_config':
                await self._validate_deployment_config(step.config_data)
            elif step.type == 'update_config':
                await self._update_environment_config(step.environment, step.config_data)
            elif step.type == 'restart_services':
                await self._restart_affected_services(step.services)
            elif step.type == 'verify_deployment':
                await self._verify_deployment_success(step.environment)
            
            return StepResult(
                step_id=step.id,
                success=True,
                completed_at=datetime.utcnow()
            )
        
        except Exception as e:
            return StepResult(
                step_id=step.id,
                success=False,
                error=str(e),
                completed_at=datetime.utcnow()
            )

class BlueGreenDeploymentStrategy:
    """Estrategia de despliegue Blue-Green"""
    
    async def create_plan(self, 
                        version: ConfigVersion,
                        environment: str,
                        current_config: Dict) -> DeploymentPlan:
        """Crear plan de despliegue Blue-Green"""
        
        steps = [
            DeploymentStep(
                id="backup",
                type="backup_current",
                description="Respaldar configuración actual",
                environment=environment
            ),
            DeploymentStep(
                id="validate",
                type="validate_config",
                description="Validar nueva configuración",
                config_data=version.config_data
            ),
            DeploymentStep(
                id="deploy_green",
                type="update_config",
                description="Desplegar en entorno Green",
                environment=f"{environment}_green",
                config_data=version.config_data
            ),
            DeploymentStep(
                id="test_green",
                type="verify_deployment",
                description="Verificar entorno Green",
                environment=f"{environment}_green"
            ),
            DeploymentStep(
                id="switch_traffic",
                type="switch_traffic",
                description="Cambiar tráfico a Green",
                from_environment=f"{environment}_blue",
                to_environment=f"{environment}_green"
            ),
            DeploymentStep(
                id="verify_production",
                type="verify_deployment",
                description="Verificar producción",
                environment=environment
            )
        ]
        
        return DeploymentPlan(
            id=str(uuid.uuid4()),
            version_id=version.id,
            environment=environment,
            strategy="blue_green",
            steps=steps,
            estimated_duration=timedelta(minutes=15),
            rollback_plan=await self._create_rollback_plan(environment, current_config)
        )
```

### 4. Sistema de Rollback Automático

```python
class RollbackManager:
    """Gestor de rollbacks automáticos y manuales"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.backup_store = BackupStore()
        self.health_monitor = HealthMonitor()
        
    async def setup_automatic_rollback(self, 
                                     environment: str,
                                     health_thresholds: Dict) -> None:
        """Configurar rollback automático basado en métricas de salud"""
        
        # Configurar monitoreo de salud
        await self.health_monitor.setup_monitoring(
            environment=environment,
            thresholds=health_thresholds,
            callback=self._handle_health_degradation
        )
    
    async def _handle_health_degradation(self, 
                                       environment: str,
                                       health_status: HealthStatus) -> None:
        """Manejar degradación de salud del sistema"""
        
        if health_status.severity >= HealthSeverity.CRITICAL:
            # Rollback automático para problemas críticos
            await self.rollback_to_previous(environment, reason="Degradación crítica de salud")
        elif health_status.severity >= HealthSeverity.WARNING:
            # Alertar pero no hacer rollback automático
            await self._send_health_alert(environment, health_status)
    
    async def rollback_to_previous(self, 
                                 environment: str,
                                 reason: str = "Manual rollback") -> RollbackResult:
        """Realizar rollback a la versión anterior"""
        
        # Obtener backup de la configuración anterior
        previous_backup = await self.backup_store.get_latest_backup(environment)
        if not previous_backup:
            raise NoBackupAvailableError(f"No hay backup disponible para {environment}")
        
        # Crear plan de rollback
        rollback_plan = await self._create_rollback_plan(environment, previous_backup)
        
        # Ejecutar rollback
        rollback_result = await self._execute_rollback(rollback_plan)
        
        # Registrar rollback
        await self._record_rollback(environment, previous_backup.version_id, reason)
        
        return rollback_result
    
    async def rollback_to_version(self, 
                                environment: str,
                                version_id: str,
                                reason: str = "Manual rollback to specific version") -> RollbackResult:
        """Realizar rollback a una versión específica"""
        
        # Obtener la versión objetivo
        target_version = await self.version_manager.get_version(version_id)
        
        # Verificar que la versión sea compatible con el entorno
        compatibility = await self._check_rollback_compatibility(target_version, environment)
        if not compatibility.is_compatible:
            raise IncompatibleRollbackError(compatibility.issues)
        
        # Crear plan de rollback
        rollback_plan = await self._create_version_rollback_plan(environment, target_version)
        
        # Ejecutar rollback
        rollback_result = await self._execute_rollback(rollback_plan)
        
        # Registrar rollback
        await self._record_rollback(environment, version_id, reason)
        
        return rollback_result
```

## Estrategia de Implementación

### Fase 1: Infraestructura Base (4-5 semanas)

1. **Implementar Gestor de Versiones**
   - Sistema de almacenamiento de versiones
   - Generación automática de números de versión
   - Tracking de cambios y metadatos

2. **Sistema de Validación**
   - Validador de esquemas
   - Reglas de negocio configurables
   - Verificación de dependencias

### Fase 2: Gestión de Despliegues (5-6 semanas)

1. **Gestor de Despliegue**
   - Estrategias de despliegue (Blue-Green, Rolling)
   - Verificación de salud post-despliegue
   - Registro de despliegues

2. **Sistema de Rollback**
   - Rollback automático basado en métricas
   - Rollback manual a versiones específicas
   - Gestión de backups

### Fase 3: Características Avanzadas (4-5 semanas)

1. **Gestión de Ramas**
   - Desarrollo paralelo de configuraciones
   - Fusión de ramas con resolución de conflictos
   - Gestión de entornos de desarrollo

2. **Auditoría y Compliance**
   - Trazabilidad completa de cambios
   - Reportes de auditoría
   - Compliance con políticas organizacionales

## Beneficios Esperados

1. **Control Total**: Gestión completa del ciclo de vida de configuraciones
2. **Seguridad**: Rollbacks automáticos y validación rigurosa
3. **Trazabilidad**: Auditoría completa de todos los cambios
4. **Colaboración**: Desarrollo paralelo con gestión de ramas
5. **Confiabilidad**: Despliegues seguros con verificación automática

Este sistema de versionado proporciona la base para una gestión robusta y confiable de las configuraciones del sistema de testing.