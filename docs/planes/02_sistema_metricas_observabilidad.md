# Plan 2: Sistema de Métricas y Observabilidad

## Resumen Ejecutivo

**Objetivo**: Implementar un sistema completo de métricas y observabilidad para monitorear, analizar y optimizar el rendimiento del sistema de testing automatizado.

**Factibilidad**: ✅ **ALTA** - Integración natural con la arquitectura configurable existente.

## Casos de Uso

1. **Monitoreo en Tiempo Real**: Seguimiento continuo del rendimiento del agente
2. **Análisis de Tendencias**: Identificación de patrones y degradación de rendimiento
3. **Alertas Inteligentes**: Notificaciones proactivas de problemas
4. **Optimización Basada en Datos**: Decisiones informadas para mejoras
5. **Reporting Ejecutivo**: Dashboards y reportes para stakeholders

## Arquitectura del Sistema

### 1. Recolector de Métricas

```python
class MetricsCollector:
    """Recolector centralizado de métricas del sistema"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        self.metrics_store = self._setup_metrics_store()
        self.real_time_processor = RealTimeMetricsProcessor()
        self.alert_manager = AlertManager(self.config.get('alerts', {}))
        
    async def collect_agent_metrics(self, agent_session: AgentSession) -> None:
        """Recolectar métricas del agente durante ejecución"""
        metrics = {
            'session_id': agent_session.id,
            'timestamp': datetime.utcnow(),
            'performance': await self._collect_performance_metrics(agent_session),
            'quality': await self._collect_quality_metrics(agent_session),
            'efficiency': await self._collect_efficiency_metrics(agent_session),
            'errors': await self._collect_error_metrics(agent_session),
            'context': await self._collect_context_metrics(agent_session)
        }
        
        # Almacenar métricas
        await self.metrics_store.store(metrics)
        
        # Procesar en tiempo real
        await self.real_time_processor.process(metrics)
        
        # Verificar alertas
        await self.alert_manager.check_thresholds(metrics)
    
    async def _collect_performance_metrics(self, session: AgentSession) -> Dict:
        """Métricas de rendimiento del agente"""
        return {
            'total_execution_time': session.total_execution_time,
            'average_step_time': session.average_step_time,
            'tokens_consumed': session.total_tokens_used,
            'tokens_per_minute': session.tokens_per_minute,
            'api_calls_count': session.api_calls_count,
            'api_response_times': session.api_response_times,
            'memory_usage': session.memory_usage,
            'cpu_usage': session.cpu_usage,
            'network_latency': session.network_latency
        }
    
    async def _collect_quality_metrics(self, session: AgentSession) -> Dict:
        """Métricas de calidad del testing"""
        return {
            'success_rate': session.success_rate,
            'bugs_found': len(session.bugs_detected),
            'bugs_by_severity': session.bugs_by_severity,
            'false_positives': session.false_positives_count,
            'coverage_percentage': session.coverage_percentage,
            'test_completeness': session.test_completeness_score,
            'accuracy_score': session.accuracy_score
        }
    
    async def _collect_efficiency_metrics(self, session: AgentSession) -> Dict:
        """Métricas de eficiencia operacional"""
        return {
            'actions_per_minute': session.actions_per_minute,
            'optimization_ratio': session.optimization_ratio,
            'batch_efficiency': session.batch_efficiency,
            'retry_rate': session.retry_rate,
            'manual_intervention_rate': session.manual_intervention_rate,
            'cost_per_test': session.cost_per_test,
            'roi_score': session.roi_score
        }
    
    async def _collect_error_metrics(self, session: AgentSession) -> Dict:
        """Métricas de errores y fallos"""
        return {
            'total_errors': len(session.errors),
            'error_types': session.error_type_distribution,
            'critical_errors': session.critical_errors_count,
            'recovery_success_rate': session.recovery_success_rate,
            'timeout_rate': session.timeout_rate,
            'crash_rate': session.crash_rate
        }
    
    async def _collect_context_metrics(self, session: AgentSession) -> Dict:
        """Métricas contextuales del testing"""
        return {
            'website_domain': session.context.domain,
            'website_purpose': session.context.purpose,
            'tech_stack': session.context.tech_stack,
            'testing_mode': session.testing_mode,
            'complexity_score': session.complexity_score,
            'site_responsiveness': session.site_responsiveness,
            'browser_type': session.browser_type,
            'viewport_size': session.viewport_size
        }

class RealTimeMetricsProcessor:
    """Procesador de métricas en tiempo real"""
    
    def __init__(self):
        self.sliding_windows = {}
        self.trend_analyzers = {}
        self.anomaly_detector = AnomalyDetector()
        
    async def process(self, metrics: Dict) -> None:
        """Procesar métricas en tiempo real"""
        # Actualizar ventanas deslizantes
        await self._update_sliding_windows(metrics)
        
        # Analizar tendencias
        trends = await self._analyze_trends(metrics)
        
        # Detectar anomalías
        anomalies = await self.anomaly_detector.detect(metrics)
        
        # Calcular métricas derivadas
        derived_metrics = await self._calculate_derived_metrics(metrics)
        
        # Publicar métricas procesadas
        await self._publish_processed_metrics({
            'original': metrics,
            'trends': trends,
            'anomalies': anomalies,
            'derived': derived_metrics
        })
    
    async def _update_sliding_windows(self, metrics: Dict) -> None:
        """Actualizar ventanas deslizantes para análisis temporal"""
        timestamp = metrics['timestamp']
        
        for metric_name, value in self._flatten_metrics(metrics).items():
            if metric_name not in self.sliding_windows:
                self.sliding_windows[metric_name] = SlidingWindow(size=100)
            
            self.sliding_windows[metric_name].add(timestamp, value)
    
    async def _analyze_trends(self, metrics: Dict) -> Dict:
        """Analizar tendencias en las métricas"""
        trends = {}
        
        for metric_name, window in self.sliding_windows.items():
            if len(window) >= 10:  # Mínimo de datos para análisis
                trend_data = {
                    'direction': window.get_trend_direction(),
                    'slope': window.get_trend_slope(),
                    'volatility': window.get_volatility(),
                    'moving_average': window.get_moving_average(period=10),
                    'percentile_95': window.get_percentile(95),
                    'percentile_5': window.get_percentile(5)
                }
                trends[metric_name] = trend_data
        
        return trends
```

### 2. Sistema de Alertas Inteligentes

```python
class AlertManager:
    """Gestor de alertas inteligentes basado en métricas"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.alert_rules = self._load_alert_rules()
        self.notification_channels = self._setup_notification_channels()
        self.alert_history = AlertHistory()
        
    async def check_thresholds(self, metrics: Dict) -> None:
        """Verificar umbrales y generar alertas"""
        for rule in self.alert_rules:
            if await self._evaluate_rule(rule, metrics):
                alert = await self._create_alert(rule, metrics)
                await self._send_alert(alert)
    
    def _load_alert_rules(self) -> List[AlertRule]:
        """Cargar reglas de alerta desde configuración"""
        rules = []
        
        # Reglas de rendimiento
        rules.append(AlertRule(
            name="high_token_usage",
            condition="metrics.performance.tokens_per_minute > 1000",
            severity="warning",
            description="Alto consumo de tokens detectado"
        ))
        
        rules.append(AlertRule(
            name="low_success_rate",
            condition="metrics.quality.success_rate < 0.8",
            severity="critical",
            description="Tasa de éxito por debajo del umbral"
        ))
        
        rules.append(AlertRule(
            name="high_error_rate",
            condition="metrics.errors.total_errors > 10",
            severity="warning",
            description="Alta tasa de errores detectada"
        ))
        
        rules.append(AlertRule(
            name="performance_degradation",
            condition="trends.performance.average_step_time.direction == 'increasing' AND trends.performance.average_step_time.slope > 0.1",
            severity="warning",
            description="Degradación de rendimiento detectada"
        ))
        
        # Cargar reglas personalizadas desde configuración
        custom_rules = self.config.get('custom_rules', [])
        for rule_config in custom_rules:
            rules.append(AlertRule.from_config(rule_config))
        
        return rules
    
    async def _evaluate_rule(self, rule: AlertRule, metrics: Dict) -> bool:
        """Evaluar si una regla de alerta se cumple"""
        try:
            # Crear contexto de evaluación
            context = {
                'metrics': metrics,
                'trends': metrics.get('trends', {}),
                'anomalies': metrics.get('anomalies', {})
            }
            
            # Evaluar condición
            return eval(rule.condition, {"__builtins__": {}}, context)
        except Exception as e:
            logger.error(f"Error evaluando regla {rule.name}: {e}")
            return False
    
    async def _create_alert(self, rule: AlertRule, metrics: Dict) -> Alert:
        """Crear alerta con contexto completo"""
        return Alert(
            id=str(uuid.uuid4()),
            rule_name=rule.name,
            severity=rule.severity,
            title=rule.description,
            description=await self._generate_alert_description(rule, metrics),
            timestamp=datetime.utcnow(),
            metrics_snapshot=metrics,
            recommended_actions=await self._get_recommended_actions(rule, metrics)
        )
    
    async def _generate_alert_description(self, rule: AlertRule, metrics: Dict) -> str:
        """Generar descripción detallada de la alerta"""
        template = self.config.get('alert_templates', {}).get(rule.name, 
            "Alerta {rule_name}: {description}\n\nMétricas actuales:\n{metrics_summary}")
        
        metrics_summary = await self._format_metrics_summary(metrics)
        
        return template.format(
            rule_name=rule.name,
            description=rule.description,
            metrics_summary=metrics_summary
        )
    
    async def _get_recommended_actions(self, rule: AlertRule, metrics: Dict) -> List[str]:
        """Obtener acciones recomendadas para la alerta"""
        action_map = {
            'high_token_usage': [
                "Revisar configuración de optimización de tokens",
                "Verificar si hay loops infinitos en el agente",
                "Considerar usar modo de testing más eficiente"
            ],
            'low_success_rate': [
                "Revisar logs de errores para identificar patrones",
                "Verificar configuración de patrones de testing",
                "Considerar ajustar timeouts y reintentos"
            ],
            'performance_degradation': [
                "Analizar métricas de red y latencia",
                "Revisar carga del sistema",
                "Verificar configuración de paralelización"
            ]
        }
        
        return action_map.get(rule.name, ["Revisar métricas y logs para más detalles"])
```

### 3. Dashboard y Visualización

```python
class MetricsDashboard:
    """Dashboard interactivo para visualización de métricas"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.data_source = MetricsDataSource()
        self.chart_generator = ChartGenerator()
        
    async def generate_real_time_dashboard(self) -> Dict:
        """Generar dashboard en tiempo real"""
        # Obtener métricas recientes
        recent_metrics = await self.data_source.get_recent_metrics(hours=1)
        
        # Generar widgets del dashboard
        widgets = {
            'performance_overview': await self._create_performance_widget(recent_metrics),
            'quality_metrics': await self._create_quality_widget(recent_metrics),
            'error_analysis': await self._create_error_widget(recent_metrics),
            'trend_analysis': await self._create_trend_widget(recent_metrics),
            'efficiency_metrics': await self._create_efficiency_widget(recent_metrics),
            'alerts_summary': await self._create_alerts_widget()
        }
        
        return {
            'timestamp': datetime.utcnow(),
            'widgets': widgets,
            'summary': await self._generate_summary(recent_metrics)
        }
    
    async def _create_performance_widget(self, metrics: List[Dict]) -> Dict:
        """Widget de métricas de rendimiento"""
        return {
            'type': 'performance_chart',
            'title': 'Rendimiento del Sistema',
            'charts': [
                await self.chart_generator.create_time_series(
                    data=metrics,
                    metric='performance.average_step_time',
                    title='Tiempo Promedio por Paso'
                ),
                await self.chart_generator.create_time_series(
                    data=metrics,
                    metric='performance.tokens_per_minute',
                    title='Tokens por Minuto'
                ),
                await self.chart_generator.create_gauge(
                    current_value=self._get_latest_value(metrics, 'performance.cpu_usage'),
                    max_value=100,
                    title='Uso de CPU (%)'
                )
            ]
        }
    
    async def _create_quality_widget(self, metrics: List[Dict]) -> Dict:
        """Widget de métricas de calidad"""
        return {
            'type': 'quality_metrics',
            'title': 'Calidad del Testing',
            'charts': [
                await self.chart_generator.create_gauge(
                    current_value=self._get_latest_value(metrics, 'quality.success_rate'),
                    max_value=1.0,
                    title='Tasa de Éxito',
                    format='percentage'
                ),
                await self.chart_generator.create_bar_chart(
                    data=self._get_latest_value(metrics, 'quality.bugs_by_severity'),
                    title='Bugs por Severidad'
                ),
                await self.chart_generator.create_time_series(
                    data=metrics,
                    metric='quality.coverage_percentage',
                    title='Cobertura de Testing'
                )
            ]
        }
    
    async def generate_executive_report(self, period: str = 'week') -> Dict:
        """Generar reporte ejecutivo"""
        # Obtener datos del período
        period_data = await self.data_source.get_period_data(period)
        
        # Calcular KPIs
        kpis = await self._calculate_kpis(period_data)
        
        # Generar insights
        insights = await self._generate_insights(period_data)
        
        # Crear recomendaciones
        recommendations = await self._generate_recommendations(period_data, insights)
        
        return {
            'period': period,
            'generated_at': datetime.utcnow(),
            'kpis': kpis,
            'insights': insights,
            'recommendations': recommendations,
            'charts': await self._generate_executive_charts(period_data)
        }
    
    async def _calculate_kpis(self, data: List[Dict]) -> Dict:
        """Calcular KPIs principales"""
        return {
            'total_tests_executed': len(data),
            'average_success_rate': np.mean([d['quality']['success_rate'] for d in data]),
            'total_bugs_found': sum([d['quality']['bugs_found'] for d in data]),
            'average_execution_time': np.mean([d['performance']['total_execution_time'] for d in data]),
            'cost_efficiency': self._calculate_cost_efficiency(data),
            'quality_score': self._calculate_quality_score(data),
            'performance_score': self._calculate_performance_score(data)
        }
```

### 4. Sistema de Reportes Automatizados

```python
class AutomatedReporting:
    """Sistema de reportes automatizados"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.report_scheduler = ReportScheduler()
        self.report_generator = ReportGenerator()
        self.notification_service = NotificationService()
        
    async def setup_scheduled_reports(self) -> None:
        """Configurar reportes programados"""
        # Reporte diario de rendimiento
        await self.report_scheduler.schedule(
            name="daily_performance",
            frequency="daily",
            time="09:00",
            report_type="performance_summary",
            recipients=self.config.get('daily_recipients', [])
        )
        
        # Reporte semanal ejecutivo
        await self.report_scheduler.schedule(
            name="weekly_executive",
            frequency="weekly",
            day="monday",
            time="08:00",
            report_type="executive_summary",
            recipients=self.config.get('executive_recipients', [])
        )
        
        # Reporte mensual de tendencias
        await self.report_scheduler.schedule(
            name="monthly_trends",
            frequency="monthly",
            day=1,
            time="10:00",
            report_type="trend_analysis",
            recipients=self.config.get('analysis_recipients', [])
        )
    
    async def generate_performance_summary(self, period: str = 'day') -> Dict:
        """Generar resumen de rendimiento"""
        data = await self._get_period_data(period)
        
        return {
            'period': period,
            'summary': {
                'total_sessions': len(data),
                'average_duration': np.mean([d['performance']['total_execution_time'] for d in data]),
                'success_rate': np.mean([d['quality']['success_rate'] for d in data]),
                'token_efficiency': self._calculate_token_efficiency(data),
                'error_rate': self._calculate_error_rate(data)
            },
            'trends': await self._analyze_period_trends(data),
            'top_issues': await self._identify_top_issues(data),
            'recommendations': await self._generate_period_recommendations(data)
        }
```

## Estrategia de Implementación

### Fase 1: Infraestructura Base (3-4 semanas)

1. **Implementar Recolector de Métricas**
   - Integrar en agente existente
   - Configurar almacenamiento de métricas
   - Establecer formato estándar

2. **Sistema de Alertas Básico**
   - Implementar reglas de alerta fundamentales
   - Configurar canales de notificación
   - Establecer umbrales iniciales

### Fase 2: Visualización y Análisis (4-5 semanas)

1. **Dashboard en Tiempo Real**
   - Implementar widgets principales
   - Configurar actualización automática
   - Integrar con sistema de alertas

2. **Análisis de Tendencias**
   - Implementar algoritmos de análisis
   - Configurar detección de anomalías
   - Establecer métricas derivadas

### Fase 3: Reportes y Optimización (3-4 semanas)

1. **Sistema de Reportes Automatizados**
   - Implementar generación de reportes
   - Configurar programación automática
   - Establecer distribución de reportes

2. **Optimización Basada en Métricas**
   - Implementar recomendaciones automáticas
   - Configurar ajustes automáticos
   - Establecer feedback loops

## Métricas Clave a Monitorear

### Rendimiento
- Tiempo de ejecución por sesión
- Tokens consumidos por minuto
- Latencia de API
- Uso de recursos del sistema

### Calidad
- Tasa de éxito de pruebas
- Bugs detectados por severidad
- Cobertura de testing
- Precisión de detección

### Eficiencia
- Costo por prueba ejecutada
- ROI del testing automatizado
- Tiempo de configuración
- Tasa de optimización

### Confiabilidad
- Tasa de errores
- Tiempo de recuperación
- Disponibilidad del sistema
- Estabilidad de configuraciones

## Beneficios Esperados

1. **Visibilidad Completa**: Monitoreo integral del sistema de testing
2. **Optimización Continua**: Mejoras basadas en datos reales
3. **Detección Temprana**: Identificación proactiva de problemas
4. **Toma de Decisiones Informada**: Datos para optimización estratégica
5. **Accountability**: Métricas claras de rendimiento y ROI

Este sistema de métricas y observabilidad proporciona la base para un testing automatizado verdaderamente optimizado y confiable.