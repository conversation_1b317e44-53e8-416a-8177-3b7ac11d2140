Test the responsive design of {website} across various device types and screen sizes.

1. Test the following key pages:
   - Homepage
   - {page1} (e.g., product listing)
   - {page2} (e.g., product detail)
   - {page3} (e.g., checkout)
   - {page4} (e.g., account settings)

2. For each page, test the following screen sizes and orientations:
   - Mobile: 320px, 375px, 414px (portrait and landscape)
   - Tablet: 768px, 1024px (portrait and landscape)
   - Desktop: 1366px, 1920px

3. For each combination, evaluate:
   - Content visibility and readability
   - Navigation usability
   - Image and media scaling
   - Form functionality
   - Touch targets and interactive elements
   - Load time and performance

4. Test specific responsive features:
   - Hamburger menu functionality on mobile
   - Collapsible sections
   - Image carousels/sliders
   - Tables or complex data representations
   - Modal windows and popups

Document all issues with screenshots, device information, and recommended fixes. Prioritize issues based on severity and frequency of user encounter.