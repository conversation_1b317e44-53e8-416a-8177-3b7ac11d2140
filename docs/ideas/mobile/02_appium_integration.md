# Integración con Appium

## Introducción

Este documento detalla la implementación técnica de la integración de Appium en QAK, manteniendo consistencia con el patrón browser-use existente.

## Dependencias y Configuración

### Dependencias Python

```python
# requirements.txt - Nuevas dependencias
Appium-Python-Client==3.1.0
selenium==4.15.0  # Compatibilidad WebDriver
webdriver-manager==4.0.1  # Gestión automática de drivers
```

### Configuración del Sistema

#### Android
```bash
# Android SDK y herramientas
brew install android-platform-tools
brew install --cask android-studio

# Variables de entorno
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/tools
```

#### iOS (macOS únicamente)
```bash
# Xcode y herramientas
xcode-select --install
brew install ios-deploy
brew install ideviceinstaller
```

## Estructura del Mobile Driver

### Archivo: `src/services/mobile_driver.py`

```python
from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import base64
import time
from typing import Optional, Dict, List, Tuple

class MobileDriver:
    """Driver móvil que mantiene interfaz similar a browser-use"""
    
    def __init__(self, platform: str = "android", device_name: str = None):
        self.platform = platform.lower()
        self.device_name = device_name
        self.driver = None
        self.wait = None
        
    def start_session(self, app_path: str = None, **kwargs):
        """Inicia sesión móvil similar a browser.new_page()"""
        if self.platform == "android":
            self._start_android_session(app_path, **kwargs)
        elif self.platform == "ios":
            self._start_ios_session(app_path, **kwargs)
        else:
            raise ValueError(f"Plataforma no soportada: {self.platform}")
            
        self.wait = WebDriverWait(self.driver, 10)
        
    def _start_android_session(self, app_path: str, **kwargs):
        """Configuración específica para Android"""
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = self.device_name or "emulator-5554"
        options.automation_name = "UiAutomator2"
        
        if app_path:
            options.app = app_path
        else:
            # Para apps ya instaladas
            options.app_package = kwargs.get('app_package')
            options.app_activity = kwargs.get('app_activity')
            
        # Configuraciones adicionales
        options.no_reset = kwargs.get('no_reset', True)
        options.full_reset = kwargs.get('full_reset', False)
        options.new_command_timeout = 300
        
        self.driver = webdriver.Remote(
            "http://localhost:4723",  # Appium server
            options=options
        )
        
    def _start_ios_session(self, app_path: str, **kwargs):
        """Configuración específica para iOS"""
        options = XCUITestOptions()
        options.platform_name = "iOS"
        options.device_name = self.device_name or "iPhone 15 Simulator"
        options.automation_name = "XCUITest"
        
        if app_path:
            options.app = app_path
        else:
            options.bundle_id = kwargs.get('bundle_id')
            
        options.no_reset = kwargs.get('no_reset', True)
        options.new_command_timeout = 300
        
        self.driver = webdriver.Remote(
            "http://localhost:4723",
            options=options
        )
    
    def screenshot(self) -> str:
        """Captura screenshot - interfaz idéntica a browser-use"""
        if not self.driver:
            raise RuntimeError("Sesión no iniciada")
        return self.driver.get_screenshot_as_base64()
    
    def tap(self, x: int, y: int):
        """Tap en coordenadas específicas"""
        self.driver.tap([(x, y)])
    
    def tap_element(self, locator: str, by: str = "xpath"):
        """Tap en elemento usando localizador"""
        element = self.wait.until(
            EC.element_to_be_clickable((getattr(By, by.upper()), locator))
        )
        element.click()
    
    def send_keys(self, locator: str, text: str, by: str = "xpath"):
        """Envía texto a elemento - similar a page.fill()"""
        element = self.wait.until(
            EC.presence_of_element_located((getattr(By, by.upper()), locator))
        )
        element.clear()
        element.send_keys(text)
    
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 1000):
        """Gesto de swipe"""
        self.driver.swipe(start_x, start_y, end_x, end_y, duration)
    
    def scroll_down(self, distance: int = None):
        """Scroll hacia abajo - equivalente a page.mouse.wheel()"""
        size = self.driver.get_window_size()
        start_x = size['width'] // 2
        start_y = size['height'] * 0.8
        end_y = size['height'] * 0.2
        self.swipe(start_x, start_y, start_x, end_y)
    
    def scroll_up(self, distance: int = None):
        """Scroll hacia arriba"""
        size = self.driver.get_window_size()
        start_x = size['width'] // 2
        start_y = size['height'] * 0.2
        end_y = size['height'] * 0.8
        self.swipe(start_x, start_y, start_x, end_y)
    
    def get_page_source(self) -> str:
        """Obtiene XML de la página - equivalente a page.content()"""
        return self.driver.page_source
    
    def find_elements(self, locator: str, by: str = "xpath") -> List:
        """Encuentra elementos - similar a page.query_selector_all()"""
        return self.driver.find_elements(getattr(By, by.upper()), locator)
    
    def wait_for_element(self, locator: str, by: str = "xpath", timeout: int = 10):
        """Espera elemento - similar a page.wait_for_selector()"""
        wait = WebDriverWait(self.driver, timeout)
        return wait.until(
            EC.presence_of_element_located((getattr(By, by.upper()), locator))
        )
    
    def install_app(self, app_path: str):
        """Instala aplicación"""
        self.driver.install_app(app_path)
    
    def remove_app(self, app_id: str):
        """Desinstala aplicación"""
        if self.platform == "android":
            self.driver.remove_app(app_id)
        else:  # iOS
            self.driver.remove_app(app_id)
    
    def activate_app(self, app_id: str):
        """Activa/abre aplicación"""
        self.driver.activate_app(app_id)
    
    def terminate_app(self, app_id: str):
        """Cierra aplicación"""
        self.driver.terminate_app(app_id)
    
    def get_device_info(self) -> Dict:
        """Información del dispositivo"""
        return {
            'platform': self.platform,
            'device_name': self.device_name,
            'screen_size': self.driver.get_window_size(),
            'orientation': self.driver.orientation
        }
    
    def close(self):
        """Cierra sesión - equivalente a browser.close()"""
        if self.driver:
            self.driver.quit()
            self.driver = None
```

## Adaptador para Browser-Use

### Archivo: `src/services/mobile_browser_adapter.py`

```python
from .mobile_driver import MobileDriver
from typing import Optional, Dict, Any

class MobileBrowserAdapter:
    """Adaptador que hace que MobileDriver sea compatible con browser-use"""
    
    def __init__(self, platform: str = "android"):
        self.mobile_driver = MobileDriver(platform)
        self._current_page = None
    
    def new_page(self, app_path: str = None, **kwargs) -> 'MobilePage':
        """Crea nueva 'página' móvil - equivalente a browser.new_page()"""
        self.mobile_driver.start_session(app_path, **kwargs)
        self._current_page = MobilePage(self.mobile_driver)
        return self._current_page
    
    def close(self):
        """Cierra browser móvil"""
        if self.mobile_driver:
            self.mobile_driver.close()

class MobilePage:
    """Página móvil que simula interfaz de Playwright Page"""
    
    def __init__(self, mobile_driver: MobileDriver):
        self.driver = mobile_driver
    
    def screenshot(self, **kwargs) -> bytes:
        """Screenshot compatible con browser-use"""
        base64_img = self.driver.screenshot()
        return base64.b64decode(base64_img)
    
    def click(self, selector: str, **kwargs):
        """Click compatible - traduce selectores CSS a XPath móvil"""
        xpath = self._css_to_mobile_xpath(selector)
        self.driver.tap_element(xpath)
    
    def fill(self, selector: str, value: str, **kwargs):
        """Fill compatible"""
        xpath = self._css_to_mobile_xpath(selector)
        self.driver.send_keys(xpath, value)
    
    def wait_for_selector(self, selector: str, **kwargs):
        """Wait compatible"""
        xpath = self._css_to_mobile_xpath(selector)
        return self.driver.wait_for_element(xpath)
    
    def content(self) -> str:
        """Contenido de página - retorna XML móvil"""
        return self.driver.get_page_source()
    
    def _css_to_mobile_xpath(self, css_selector: str) -> str:
        """Convierte selectores CSS a XPath móvil"""
        # Mapeo básico - se puede expandir
        mappings = {
            'button': '//android.widget.Button | //XCUIElementTypeButton',
            'input': '//android.widget.EditText | //XCUIElementTypeTextField',
            '[data-testid="{}"]': '//*[@content-desc="{}" or @resource-id="*{}*"]'
        }
        
        # Lógica de conversión más sofisticada aquí
        # Por ahora, retorna XPath genérico
        return f"//*[contains(@text, '{css_selector}') or contains(@content-desc, '{css_selector}')]"
    
    def close(self):
        """Cierra página móvil"""
        self.driver.close()
```

## Configuración de Appium Server

### Archivo: `src/config/mobile_config.py`

```python
import subprocess
import time
import requests
from typing import Dict, Optional

class AppiumServerManager:
    """Gestiona el servidor Appium"""
    
    def __init__(self, port: int = 4723):
        self.port = port
        self.process = None
    
    def start_server(self, **kwargs) -> bool:
        """Inicia servidor Appium"""
        if self.is_running():
            return True
            
        cmd = [
            'appium',
            '--port', str(self.port),
            '--log-level', 'info'
        ]
        
        # Configuraciones adicionales
        if kwargs.get('allow_insecure'):
            cmd.extend(['--allow-insecure', 'chromedriver_autodownload'])
            
        try:
            self.process = subprocess.Popen(cmd)
            time.sleep(5)  # Esperar inicio
            return self.is_running()
        except Exception as e:
            print(f"Error iniciando Appium: {e}")
            return False
    
    def stop_server(self):
        """Detiene servidor Appium"""
        if self.process:
            self.process.terminate()
            self.process = None
    
    def is_running(self) -> bool:
        """Verifica si Appium está corriendo"""
        try:
            response = requests.get(f'http://localhost:{self.port}/status', timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_status(self) -> Dict:
        """Estado del servidor Appium"""
        try:
            response = requests.get(f'http://localhost:{self.port}/status')
            return response.json()
        except:
            return {'ready': False}
```

## Integración con QAK

### Modificaciones en `src/services/browser_service.py`

```python
# Agregar soporte móvil
from .mobile_browser_adapter import MobileBrowserAdapter

class BrowserService:
    def __init__(self):
        self.web_browser = None  # Browser existente
        self.mobile_browser = None  # Nuevo browser móvil
        self.current_mode = "web"  # "web" o "mobile"
    
    def switch_to_mobile(self, platform: str = "android"):
        """Cambia a modo móvil"""
        self.mobile_browser = MobileBrowserAdapter(platform)
        self.current_mode = "mobile"
    
    def switch_to_web(self):
        """Cambia a modo web"""
        self.current_mode = "web"
    
    def new_page(self, **kwargs):
        """Crea página según modo actual"""
        if self.current_mode == "mobile":
            return self.mobile_browser.new_page(**kwargs)
        else:
            return self.web_browser.new_page(**kwargs)
```

## Próximos Pasos

1. **Testing**: Crear tests unitarios para cada componente
2. **Documentación**: Guías de uso para desarrolladores
3. **Optimización**: Performance y manejo de errores
4. **Integración**: Conectar con el sistema de prompts AI

Ver `03_device_management.md` para gestión de dispositivos y emuladores.