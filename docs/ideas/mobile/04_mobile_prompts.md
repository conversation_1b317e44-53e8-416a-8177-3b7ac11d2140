# Adaptación de Prompts AI para Testing Móvil

## Introducción

Este documento detalla cómo adaptar los prompts y la inteligencia artificial de QAK para trabajar efectivamente con interfaces móviles, manteniendo la misma calidad de análisis visual y generación de acciones.

## Diferencias Clave: Web vs Móvil

### Elementos de Interfaz

| Web | Móvil | Consideraciones |
|-----|-------|----------------|
| `<button>` | `Button`, `ImageButton` | Diferentes tipos de botones |
| `<input>` | `EditText`, `TextField` | Campos de entrada nativos |
| `<select>` | `Spinner`, `Picker` | Selectores específicos de plataforma |
| `<div>` | `View`, `LinearLayout` | Contenedores nativos |
| CSS Selectors | XPath/Accessibility IDs | Diferentes estrategias de localización |

### Gestos y Acciones

| Web | Móvil | Implementación |
|-----|-------|---------------|
| `click()` | `tap()` | Toque simple |
| `scroll()` | `swipe()` | Gestos direccionales |
| `hover()` | `long_press()` | Interacciones prolongadas |
| `drag()` | `drag_and_drop()` | Arrastrar elementos |
| - | `pinch()` | Zoom específico móvil |

## Sistema de Prompts Móviles

### Archivo: `prompts/mobile-automation/system-prompt.md`

```markdown
# Sistema de Prompts para Automatización Móvil

Eres un agente AI especializado en testing de aplicaciones móviles. Tu objetivo es analizar screenshots de apps móviles y generar acciones precisas para completar tareas específicas.

## Contexto de Trabajo

- **Plataforma**: {platform} (Android/iOS)
- **Dispositivo**: {device_name}
- **Resolución**: {screen_width}x{screen_height}
- **Orientación**: {orientation}
- **App**: {app_name} ({package_name})

## Capacidades Disponibles

### Acciones Básicas
- `tap(x, y)` - Toque en coordenadas específicas
- `tap_element(locator)` - Toque en elemento usando localizador
- `send_keys(locator, text)` - Enviar texto a campo de entrada
- `swipe(start_x, start_y, end_x, end_y)` - Gesto de deslizamiento
- `long_press(x, y)` - Presión prolongada
- `scroll_down()` / `scroll_up()` - Desplazamiento vertical
- `back()` - Botón atrás (Android)
- `home()` - Botón inicio

### Localizadores Móviles
- **Android**: 
  - `//android.widget.Button[@text='Texto']`
  - `//*[@content-desc='descripción']`
  - `//*[@resource-id='com.app:id/elemento']`
- **iOS**:
  - `//XCUIElementTypeButton[@name='Texto']`
  - `//*[@label='etiqueta']`
  - `//*[@identifier='elemento_id']`

## Análisis de Screenshots

Cuando analices un screenshot móvil:

1. **Identifica la estructura**: Barra de estado, navegación, contenido principal
2. **Localiza elementos interactivos**: Botones, campos, listas, menús
3. **Considera el contexto**: Estado de la app, pantalla actual, flujo de navegación
4. **Evalúa accesibilidad**: Elementos con descripciones, IDs únicos
5. **Planifica acciones**: Secuencia lógica para completar la tarea

## Patrones Comunes Móviles

### Navegación
- **Tab Bar** (iOS) / **Bottom Navigation** (Android): Navegación principal
- **Navigation Drawer**: Menú lateral deslizable
- **Back Stack**: Navegación jerárquica con botón atrás
- **Modal Sheets**: Pantallas superpuestas

### Interacciones
- **Pull to Refresh**: Deslizar hacia abajo para actualizar
- **Infinite Scroll**: Carga automática al llegar al final
- **Swipe Actions**: Acciones en elementos de lista
- **Pinch to Zoom**: Zoom en imágenes/mapas

### Estados de UI
- **Loading States**: Indicadores de carga
- **Empty States**: Pantallas sin contenido
- **Error States**: Manejo de errores
- **Offline States**: Funcionalidad sin conexión

## Formato de Respuesta

Tu respuesta debe seguir este formato JSON:

```json
{
  "analysis": "Descripción de lo que ves en el screenshot",
  "current_screen": "Nombre/tipo de pantalla actual",
  "interactive_elements": [
    {
      "type": "button|input|list_item|etc",
      "text": "Texto visible",
      "description": "Descripción del elemento",
      "coordinates": {"x": 100, "y": 200},
      "locator": "xpath_o_selector"
    }
  ],
  "recommended_action": {
    "action": "tap|swipe|send_keys|etc",
    "target": "elemento_objetivo",
    "parameters": {"x": 100, "y": 200, "text": "valor"},
    "reasoning": "Por qué esta acción"
  },
  "next_steps": ["Paso 1", "Paso 2", "Paso 3"],
  "confidence": 0.95
}
```

## Consideraciones Especiales

### Android
- Botón "Atrás" siempre disponible
- Menú de opciones (3 puntos)
- Notificaciones deslizables
- Permisos del sistema

### iOS
- Navegación por gestos (swipe desde bordes)
- Control Center y Notification Center
- Botón "Cancelar" en modales
- Haptic feedback

### Responsive Design
- Diferentes tamaños de pantalla
- Orientación portrait/landscape
- Densidades de píxeles variables
- Safe areas (iPhone X+)

## Manejo de Errores

Si encuentras problemas:
- **Elemento no encontrado**: Intentar localizadores alternativos
- **Acción fallida**: Verificar estado de la app
- **Pantalla inesperada**: Analizar contexto y adaptar estrategia
- **Performance lenta**: Agregar esperas apropiadas
```

### Archivo: `prompts/mobile-automation/task-analysis.md`

```markdown
# Análisis de Tareas Móviles

Cuando recibas una tarea para testing móvil, sigue este proceso de análisis:

## 1. Descomposición de la Tarea

### Ejemplo: "Buscar un producto y agregarlo al carrito"

**Pasos identificados:**
1. Abrir app de e-commerce
2. Localizar función de búsqueda
3. Ingresar término de búsqueda
4. Seleccionar producto de resultados
5. Revisar detalles del producto
6. Agregar al carrito
7. Verificar que se agregó correctamente

## 2. Mapeo de Pantallas

**Flujo esperado:**
- Pantalla inicial → Búsqueda → Resultados → Detalle → Carrito

**Pantallas alternativas:**
- Login/registro si es requerido
- Pantallas de error o sin resultados
- Modales de confirmación

## 3. Identificación de Elementos

### Elementos Críticos
- Campo de búsqueda
- Botón de búsqueda
- Lista de resultados
- Botón "Agregar al carrito"
- Indicador de carrito

### Localizadores Probables
```xpath
// Campo de búsqueda
//android.widget.EditText[@hint='Buscar productos']
//XCUIElementTypeSearchField

// Botón de búsqueda
//android.widget.Button[@text='Buscar']
//XCUIElementTypeButton[@name='Search']

// Elementos de lista
//android.widget.RecyclerView//android.widget.TextView
//XCUIElementTypeCollectionView//XCUIElementTypeCell

// Botón agregar al carrito
//*[@text='Agregar al carrito' or @text='Add to Cart']
```

## 4. Estrategias de Verificación

### Verificaciones Visuales
- Screenshot antes y después de cada acción
- Comparación de elementos en pantalla
- Detección de cambios de estado

### Verificaciones Funcionales
- Navegación exitosa entre pantallas
- Actualización de contadores (carrito, notificaciones)
- Mensajes de confirmación

## 5. Manejo de Variabilidad

### Diferentes Resoluciones
- Usar coordenadas relativas (porcentajes)
- Localizadores basados en contenido, no posición
- Verificar elementos visibles en viewport

### Estados de la App
- Primera vez vs usuario existente
- Con/sin conexión a internet
- Diferentes idiomas/localizaciones
- Modo claro/oscuro

### Timing y Performance
- Esperas inteligentes para carga de contenido
- Detección de estados de loading
- Timeouts apropiados para cada acción
```

### Archivo: `prompts/mobile-automation/element-detection.md`

```markdown
# Detección de Elementos Móviles

## Estrategias de Localización

### Jerarquía de Preferencia

1. **Accessibility IDs** (Más confiable)
   ```xpath
   // Android
   //*[@content-desc='unique_identifier']
   
   // iOS
   //*[@identifier='unique_identifier']
   ```

2. **Resource IDs** (Android específico)
   ```xpath
   //*[@resource-id='com.app.package:id/element_id']
   ```

3. **Texto Visible** (Dependiente del idioma)
   ```xpath
   //*[@text='Texto Exacto']
   //*[contains(@text, 'Texto Parcial')]
   ```

4. **Tipo de Elemento + Atributos**
   ```xpath
   //android.widget.Button[@enabled='true']
   //XCUIElementTypeButton[@visible='true']
   ```

5. **Posición Relativa** (Último recurso)
   ```xpath
   //android.widget.LinearLayout[1]/android.widget.Button[2]
   ```

## Patrones de Elementos Comunes

### Botones
```xpath
// Botón primario
//android.widget.Button[@text='Continuar']
//XCUIElementTypeButton[@name='Continue']

// Botón con ícono
//*[@content-desc='Botón de búsqueda']
//*[@identifier='search_button']

// Botón flotante (FAB)
//android.support.design.widget.FloatingActionButton
```

### Campos de Entrada
```xpath
// Campo de texto
//android.widget.EditText[@hint='Ingresa tu email']
//XCUIElementTypeTextField[@placeholder='Enter email']

// Campo de contraseña
//android.widget.EditText[@password='true']
//XCUIElementTypeSecureTextField

// Campo numérico
//android.widget.EditText[@inputType='number']
//XCUIElementTypeTextField[@keyboardType='NumberPad']
```

### Listas y Elementos de Lista
```xpath
// Lista completa
//android.widget.RecyclerView
//XCUIElementTypeCollectionView

// Elemento específico de lista
//android.widget.RecyclerView//android.widget.TextView[@text='Item específico']
//XCUIElementTypeCollectionView//XCUIElementTypeCell[contains(@name, 'Item')]

// Lista con scroll infinito
//android.widget.RecyclerView[last()]
```

### Navegación
```xpath
// Tab bar
//android.widget.TabHost//android.widget.TextView[@text='Inicio']
//XCUIElementTypeTabBar//XCUIElementTypeButton[@name='Home']

// Navigation drawer
//android.support.v4.widget.DrawerLayout
//android.widget.ListView[@resource-id='navigation_menu']

// Toolbar/ActionBar
//android.widget.Toolbar//android.widget.TextView
//XCUIElementTypeNavigationBar//XCUIElementTypeButton
```

## Técnicas Avanzadas de Detección

### Elementos Dinámicos
```xpath
// Contenido que cambia
//*[starts-with(@text, 'Producto:')]
//*[contains(@resource-id, 'dynamic_')]

// Elementos con timestamps
//*[contains(@text, '2024') and contains(@text, ':')]

// Contadores y badges
//*[@text and string-length(@text) <= 3 and number(@text) = @text]
```

### Elementos Condicionales
```xpath
// Elementos que pueden estar presentes
//*[@text='Opcional' and @displayed='true']

// Elementos en diferentes estados
//android.widget.Switch[@checked='true']
//XCUIElementTypeSwitch[@value='1']

// Elementos con loading
//*[@text='Cargando...' or @content-desc='Loading']
```

### Elementos por Contexto
```xpath
// Dentro de un contenedor específico
//android.widget.LinearLayout[@resource-id='header']//android.widget.Button

// Hermanos de un elemento conocido
//*[@text='Etiqueta conocida']/following-sibling::android.widget.EditText

// Elementos padre
//*[@text='Elemento hijo']/../android.widget.Button
```

## Validación de Elementos

### Verificaciones Básicas
```python
# Elemento existe y es visible
element.is_displayed()

# Elemento es interactivo
element.is_enabled()

# Elemento tiene el texto esperado
element.text == "Texto esperado"

# Elemento tiene atributos correctos
element.get_attribute("content-desc") == "Descripción"
```

### Verificaciones Avanzadas
```python
# Elemento está en el viewport
location = element.location
size = driver.get_window_size()
is_visible = (0 <= location['x'] <= size['width'] and 
              0 <= location['y'] <= size['height'])

# Elemento no está oculto por otros
# (requiere análisis más complejo)
```

## Manejo de Errores de Localización

### Estrategias de Fallback
```python
def find_element_robust(driver, primary_locator, fallback_locators):
    """Busca elemento con múltiples estrategias"""
    
    # Intentar localizador principal
    try:
        return driver.find_element(By.XPATH, primary_locator)
    except NoSuchElementException:
        pass
    
    # Intentar localizadores de respaldo
    for locator in fallback_locators:
        try:
            return driver.find_element(By.XPATH, locator)
        except NoSuchElementException:
            continue
    
    # Si nada funciona, buscar por texto parcial
    try:
        return driver.find_element(By.XPATH, "//*[contains(@text, 'texto_clave')]")
    except NoSuchElementException:
        raise ElementNotFoundError("Elemento no encontrado con ninguna estrategia")
```

### Logging y Debugging
```python
def log_element_info(driver, locator):
    """Registra información del elemento para debugging"""
    try:
        element = driver.find_element(By.XPATH, locator)
        info = {
            'found': True,
            'text': element.text,
            'displayed': element.is_displayed(),
            'enabled': element.is_enabled(),
            'location': element.location,
            'size': element.size,
            'attributes': {
                'content-desc': element.get_attribute('content-desc'),
                'resource-id': element.get_attribute('resource-id'),
                'class': element.get_attribute('class')
            }
        }
    except NoSuchElementException:
        info = {'found': False, 'locator': locator}
    
    print(f"Element info: {json.dumps(info, indent=2)}")
    return info
```
```

### Archivo: `prompts/mobile-automation/gesture-patterns.md`

```markdown
# Patrones de Gestos Móviles

## Gestos Básicos

### Tap (Toque Simple)
```python
# Coordenadas específicas
driver.tap([(x, y)])

# En elemento
element.click()

# Múltiples toques
driver.tap([(x1, y1), (x2, y2)])
```

**Casos de uso:**
- Botones, enlaces, elementos de lista
- Activar campos de entrada
- Seleccionar opciones

### Long Press (Presión Prolongada)
```python
# Coordenadas específicas
driver.long_press_keycode(x, y, duration=1000)

# En elemento
action = TouchAction(driver)
action.long_press(element, duration=1000).release().perform()
```

**Casos de uso:**
- Menús contextuales
- Selección de texto
- Acciones secundarias

### Swipe (Deslizamiento)
```python
# Swipe básico
driver.swipe(start_x, start_y, end_x, end_y, duration=1000)

# Swipe en elemento
action = TouchAction(driver)
action.press(element).move_to(target).release().perform()
```

**Direcciones comunes:**
- **Arriba**: Scroll hacia abajo, cerrar modales
- **Abajo**: Scroll hacia arriba, pull-to-refresh
- **Izquierda**: Siguiente página, eliminar elemento
- **Derecha**: Página anterior, menú lateral

## Gestos de Navegación

### Scroll Vertical
```python
def scroll_down(driver, distance=0.5):
    """Scroll hacia abajo"""
    size = driver.get_window_size()
    start_x = size['width'] // 2
    start_y = int(size['height'] * 0.8)
    end_y = int(size['height'] * (0.8 - distance))
    driver.swipe(start_x, start_y, start_x, end_y)

def scroll_up(driver, distance=0.5):
    """Scroll hacia arriba"""
    size = driver.get_window_size()
    start_x = size['width'] // 2
    start_y = int(size['height'] * 0.2)
    end_y = int(size['height'] * (0.2 + distance))
    driver.swipe(start_x, start_y, start_x, end_y)
```

### Scroll Horizontal
```python
def scroll_left(driver):
    """Scroll hacia la izquierda (siguiente)"""
    size = driver.get_window_size()
    start_x = int(size['width'] * 0.8)
    end_x = int(size['width'] * 0.2)
    y = size['height'] // 2
    driver.swipe(start_x, y, end_x, y)

def scroll_right(driver):
    """Scroll hacia la derecha (anterior)"""
    size = driver.get_window_size()
    start_x = int(size['width'] * 0.2)
    end_x = int(size['width'] * 0.8)
    y = size['height'] // 2
    driver.swipe(start_x, y, end_x, y)
```

### Pull to Refresh
```python
def pull_to_refresh(driver):
    """Gesto de pull-to-refresh"""
    size = driver.get_window_size()
    start_x = size['width'] // 2
    start_y = int(size['height'] * 0.2)
    end_y = int(size['height'] * 0.8)
    
    # Swipe lento hacia abajo
    driver.swipe(start_x, start_y, start_x, end_y, duration=2000)
```

## Gestos Avanzados

### Pinch to Zoom
```python
def pinch_zoom_in(driver, element=None):
    """Zoom in con pinch"""
    if element:
        location = element.location
        size = element.size
        center_x = location['x'] + size['width'] // 2
        center_y = location['y'] + size['height'] // 2
    else:
        screen_size = driver.get_window_size()
        center_x = screen_size['width'] // 2
        center_y = screen_size['height'] // 2
    
    # Dos dedos se separan desde el centro
    finger1_start = (center_x - 50, center_y)
    finger1_end = (center_x - 150, center_y)
    finger2_start = (center_x + 50, center_y)
    finger2_end = (center_x + 150, center_y)
    
    action1 = TouchAction(driver)
    action2 = TouchAction(driver)
    
    action1.press(x=finger1_start[0], y=finger1_start[1]).move_to(x=finger1_end[0], y=finger1_end[1]).release()
    action2.press(x=finger2_start[0], y=finger2_start[1]).move_to(x=finger2_end[0], y=finger2_end[1]).release()
    
    MultiAction(driver).add(action1).add(action2).perform()

def pinch_zoom_out(driver, element=None):
    """Zoom out con pinch"""
    # Implementación similar pero dedos se juntan
    pass
```

### Drag and Drop
```python
def drag_and_drop(driver, source_element, target_element):
    """Arrastra elemento desde origen a destino"""
    action = TouchAction(driver)
    action.long_press(source_element)
    action.move_to(target_element)
    action.release()
    action.perform()

def drag_to_coordinates(driver, element, target_x, target_y):
    """Arrastra elemento a coordenadas específicas"""
    action = TouchAction(driver)
    action.long_press(element)
    action.move_to(x=target_x, y=target_y)
    action.release()
    action.perform()
```

## Patrones de Interacción Específicos

### Navegación por Tabs
```python
def switch_to_tab(driver, tab_name):
    """Cambia a tab específico"""
    tab_locator = f"//*[@text='{tab_name}' or @content-desc='{tab_name}']"
    tab_element = driver.find_element(By.XPATH, tab_locator)
    tab_element.click()

def swipe_between_tabs(driver, direction='left'):
    """Navega entre tabs con swipe"""
    # Buscar el contenedor de tabs
    tab_container = driver.find_element(By.XPATH, "//android.widget.TabHost | //XCUIElementTypeTabBar")
    
    if direction == 'left':
        scroll_left(driver)
    else:
        scroll_right(driver)
```

### Manejo de Listas
```python
def scroll_to_list_item(driver, item_text, max_scrolls=10):
    """Busca elemento en lista con scroll"""
    for _ in range(max_scrolls):
        try:
            element = driver.find_element(By.XPATH, f"//*[@text='{item_text}']")
            return element
        except NoSuchElementException:
            scroll_down(driver, distance=0.3)
    
    raise NoSuchElementException(f"Elemento '{item_text}' no encontrado después de {max_scrolls} scrolls")

def swipe_list_item_action(driver, item_element, direction='left'):
    """Acción de swipe en elemento de lista"""
    location = item_element.location
    size = item_element.size
    
    start_x = location['x'] + size['width'] - 10
    end_x = location['x'] + 10
    y = location['y'] + size['height'] // 2
    
    if direction == 'right':
        start_x, end_x = end_x, start_x
    
    driver.swipe(start_x, y, end_x, y, duration=500)
```

### Manejo de Modales y Overlays
```python
def dismiss_modal(driver, method='swipe_down'):
    """Cierra modal con diferentes métodos"""
    if method == 'swipe_down':
        # Swipe hacia abajo desde la parte superior
        size = driver.get_window_size()
        start_x = size['width'] // 2
        start_y = int(size['height'] * 0.1)
        end_y = int(size['height'] * 0.9)
        driver.swipe(start_x, start_y, start_x, end_y)
    
    elif method == 'tap_outside':
        # Tap fuera del modal (esquinas)
        size = driver.get_window_size()
        driver.tap([(10, 10)])  # Esquina superior izquierda
    
    elif method == 'close_button':
        # Buscar botón de cerrar
        close_button = driver.find_element(By.XPATH, 
            "//*[@text='Cerrar' or @text='Close' or @content-desc='Close' or @content-desc='Cerrar']")
        close_button.click()
```

## Consideraciones de Performance

### Timing y Esperas
```python
def wait_for_element_and_gesture(driver, locator, gesture_func, timeout=10):
    """Espera elemento y ejecuta gesto"""
    wait = WebDriverWait(driver, timeout)
    element = wait.until(EC.element_to_be_clickable((By.XPATH, locator)))
    
    # Pequeña pausa para estabilidad
    time.sleep(0.5)
    
    gesture_func(element)
    
    # Pausa después del gesto
    time.sleep(0.5)
```

### Detección de Animaciones
```python
def wait_for_animation_complete(driver, timeout=5):
    """Espera que terminen las animaciones"""
    # Tomar screenshots consecutivos y compararlos
    previous_screenshot = driver.get_screenshot_as_png()
    
    for _ in range(timeout * 2):  # Check every 0.5 seconds
        time.sleep(0.5)
        current_screenshot = driver.get_screenshot_as_png()
        
        if previous_screenshot == current_screenshot:
            return True  # No hay cambios, animación terminó
        
        previous_screenshot = current_screenshot
    
    return False  # Timeout
```

## Debugging de Gestos

### Visualización de Gestos
```python
def log_gesture(gesture_type, start_coords, end_coords=None, duration=None):
    """Registra información del gesto para debugging"""
    log_info = {
        'gesture': gesture_type,
        'start': start_coords,
        'timestamp': time.time()
    }
    
    if end_coords:
        log_info['end'] = end_coords
        log_info['distance'] = math.sqrt(
            (end_coords[0] - start_coords[0])**2 + 
            (end_coords[1] - start_coords[1])**2
        )
    
    if duration:
        log_info['duration'] = duration
    
    print(f"Gesture executed: {json.dumps(log_info)}")
```

### Captura de Estado
```python
def capture_gesture_context(driver, gesture_name):
    """Captura contexto antes y después del gesto"""
    context = {
        'gesture': gesture_name,
        'before': {
            'screenshot': driver.get_screenshot_as_base64(),
            'page_source': driver.page_source,
            'window_size': driver.get_window_size()
        }
    }
    
    return context

def complete_gesture_context(context, driver):
    """Completa contexto después del gesto"""
    context['after'] = {
        'screenshot': driver.get_screenshot_as_base64(),
        'page_source': driver.page_source,
        'window_size': driver.get_window_size()
    }
    
    return context
```
```

## Integración con QAK

### Archivo: `src/services/mobile_prompt_service.py`

```python
from typing import Dict, List, Optional
import json
import base64
from PIL import Image
import io

class MobilePromptService:
    """Servicio para generar y procesar prompts móviles"""
    
    def __init__(self, llm_service):
        self.llm_service = llm_service
        self.mobile_prompts = self._load_mobile_prompts()
    
    def analyze_mobile_screenshot(self, screenshot_b64: str, task: str, 
                                platform: str, device_info: Dict) -> Dict:
        """Analiza screenshot móvil y genera acciones"""
        
        # Preparar contexto móvil
        context = {
            'platform': platform,
            'device_name': device_info.get('name', 'Unknown'),
            'screen_width': device_info.get('screen_size', {}).get('width', 0),
            'screen_height': device_info.get('screen_size', {}).get('height', 0),
            'orientation': device_info.get('orientation', 'portrait'),
            'app_name': device_info.get('app_name', 'Unknown App'),
            'package_name': device_info.get('package_name', '')
        }
        
        # Construir prompt específico para móvil
        system_prompt = self._build_mobile_system_prompt(context)
        user_prompt = self._build_mobile_user_prompt(task, screenshot_b64)
        
        # Enviar a LLM
        response = self.llm_service.generate_response(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            response_format='json'
        )
        
        return self._parse_mobile_response(response)
    
    def _build_mobile_system_prompt(self, context: Dict) -> str:
        """Construye prompt del sistema para móvil"""
        template = self.mobile_prompts['system_prompt']
        return template.format(**context)
    
    def _build_mobile_user_prompt(self, task: str, screenshot_b64: str) -> str:
        """Construye prompt del usuario para móvil"""
        return f"""
Tarea a completar: {task}

Analiza el siguiente screenshot de la aplicación móvil y determina la mejor acción para avanzar hacia completar la tarea.

Screenshot: data:image/png;base64,{screenshot_b64}

Proporciona tu análisis en formato JSON según el esquema especificado.
"""
    
    def _parse_mobile_response(self, response: str) -> Dict:
        """Parsea respuesta del LLM para móvil"""
        try:
            parsed = json.loads(response)
            
            # Validar estructura requerida
            required_fields = ['analysis', 'current_screen', 'recommended_action']
            for field in required_fields:
                if field not in parsed:
                    raise ValueError(f"Campo requerido '{field}' no encontrado")
            
            # Validar acción recomendada
            action = parsed['recommended_action']
            if 'action' not in action or 'parameters' not in action:
                raise ValueError("Acción recomendada incompleta")
            
            return parsed
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Respuesta no es JSON válido: {e}")
    
    def generate_mobile_test_steps(self, user_story: str, platform: str) -> List[Dict]:
        """Genera pasos de test para historia de usuario móvil"""
        
        prompt = f"""
Genera pasos detallados de testing móvil para la siguiente historia de usuario en {platform}:

{user_story}

Cada paso debe incluir:
- Descripción de la acción
- Elementos a buscar
- Localizadores específicos para {platform}
- Criterios de verificación
- Manejo de errores potenciales

Formato de respuesta: JSON con array de pasos.
"""
        
        response = self.llm_service.generate_response(
            system_prompt=self.mobile_prompts['test_generation'],
            user_prompt=prompt,
            response_format='json'
        )
        
        return json.loads(response)
    
    def _load_mobile_prompts(self) -> Dict[str, str]:
        """Carga prompts móviles desde archivos"""
        prompts = {}
        
        prompt_files = {
            'system_prompt': 'prompts/mobile-automation/system-prompt.md',
            'task_analysis': 'prompts/mobile-automation/task-analysis.md',
            'element_detection': 'prompts/mobile-automation/element-detection.md',
            'gesture_patterns': 'prompts/mobile-automation/gesture-patterns.md',
            'test_generation': 'prompts/mobile-automation/test-generation.md'
        }
        
        for key, file_path in prompt_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    prompts[key] = f.read()
            except FileNotFoundError:
                print(f"Archivo de prompt no encontrado: {file_path}")
                prompts[key] = ""
        
        return prompts
```

## Próximos Pasos

1. **Testing de Prompts**: Validar efectividad con casos reales
2. **Optimización**: Ajustar prompts basado en resultados
3. **Localización**: Soporte para múltiples idiomas
4. **Especialización**: Prompts específicos por tipo de app

Ver `05_implementation_roadmap.md` para plan de implementación completo.