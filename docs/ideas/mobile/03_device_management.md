# Gestión de Dispositivos y Emuladores

## Introducción

Este documento detalla la gestión automática de dispositivos móviles, emuladores y simuladores para QAK, incluyendo configuración, detección y orquestación.

## Arquitectura de Device Management

```
Device Management Layer

┌─────────────────────────────────────────────────────────────┐
│                Device Manager Core                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ Device      │  │ Emulator    │  │ Health Monitor      │ │
│  │ Discovery   │  │ Manager     │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                Platform Managers                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ Android     │  │ iOS         │  │ Cloud Devices       │ │
│  │ Manager     │  │ Manager     │  │ (BrowserStack, etc) │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Device Manager Core

### Archivo: `src/services/device_manager.py`

```python
import subprocess
import json
import time
import re
from typing import List, Dict, Optional, Union
from dataclasses import dataclass
from enum import Enum

class DeviceType(Enum):
    ANDROID_EMULATOR = "android_emulator"
    ANDROID_REAL = "android_real"
    IOS_SIMULATOR = "ios_simulator"
    IOS_REAL = "ios_real"
    CLOUD_DEVICE = "cloud_device"

class DeviceStatus(Enum):
    AVAILABLE = "available"
    BUSY = "busy"
    OFFLINE = "offline"
    BOOTING = "booting"
    ERROR = "error"

@dataclass
class Device:
    """Representación unificada de dispositivo"""
    id: str
    name: str
    type: DeviceType
    platform: str  # "android" o "ios"
    version: str
    status: DeviceStatus
    capabilities: Dict
    connection_info: Dict
    
class DeviceManager:
    """Gestor principal de dispositivos"""
    
    def __init__(self):
        self.android_manager = AndroidDeviceManager()
        self.ios_manager = IOSDeviceManager()
        self.cloud_manager = CloudDeviceManager()
        self._device_cache = {}
        self._last_scan = 0
        self.scan_interval = 30  # segundos
    
    def discover_devices(self, force_refresh: bool = False) -> List[Device]:
        """Descubre todos los dispositivos disponibles"""
        current_time = time.time()
        
        if not force_refresh and (current_time - self._last_scan) < self.scan_interval:
            return list(self._device_cache.values())
        
        devices = []
        
        # Dispositivos Android
        try:
            android_devices = self.android_manager.discover()
            devices.extend(android_devices)
        except Exception as e:
            print(f"Error descubriendo dispositivos Android: {e}")
        
        # Dispositivos iOS (solo en macOS)
        try:
            ios_devices = self.ios_manager.discover()
            devices.extend(ios_devices)
        except Exception as e:
            print(f"Error descubriendo dispositivos iOS: {e}")
        
        # Dispositivos en la nube
        try:
            cloud_devices = self.cloud_manager.discover()
            devices.extend(cloud_devices)
        except Exception as e:
            print(f"Error descubriendo dispositivos en la nube: {e}")
        
        # Actualizar cache
        self._device_cache = {device.id: device for device in devices}
        self._last_scan = current_time
        
        return devices
    
    def get_device(self, device_id: str) -> Optional[Device]:
        """Obtiene dispositivo específico"""
        if device_id not in self._device_cache:
            self.discover_devices(force_refresh=True)
        return self._device_cache.get(device_id)
    
    def get_available_devices(self, platform: str = None) -> List[Device]:
        """Obtiene dispositivos disponibles"""
        devices = self.discover_devices()
        available = [d for d in devices if d.status == DeviceStatus.AVAILABLE]
        
        if platform:
            available = [d for d in available if d.platform == platform.lower()]
        
        return available
    
    def reserve_device(self, device_id: str) -> bool:
        """Reserva dispositivo para uso exclusivo"""
        device = self.get_device(device_id)
        if not device or device.status != DeviceStatus.AVAILABLE:
            return False
        
        device.status = DeviceStatus.BUSY
        return True
    
    def release_device(self, device_id: str) -> bool:
        """Libera dispositivo reservado"""
        device = self.get_device(device_id)
        if not device:
            return False
        
        device.status = DeviceStatus.AVAILABLE
        return True
    
    def get_best_device(self, requirements: Dict) -> Optional[Device]:
        """Selecciona el mejor dispositivo según requisitos"""
        platform = requirements.get('platform', 'android')
        min_version = requirements.get('min_version')
        device_type = requirements.get('type')  # 'emulator', 'real', etc.
        
        available = self.get_available_devices(platform)
        
        # Filtrar por tipo si se especifica
        if device_type:
            if device_type == 'emulator':
                available = [d for d in available if 'emulator' in d.type.value]
            elif device_type == 'real':
                available = [d for d in available if 'real' in d.type.value]
        
        # Filtrar por versión mínima
        if min_version:
            available = [d for d in available if self._version_compare(d.version, min_version) >= 0]
        
        # Priorizar emuladores sobre dispositivos reales para testing automatizado
        available.sort(key=lambda d: (d.type.value, d.name))
        
        return available[0] if available else None
    
    def _version_compare(self, version1: str, version2: str) -> int:
        """Compara versiones (1 > 0 > -1)"""
        def normalize(v):
            return [int(x) for x in re.sub(r'[^0-9.]', '', v).split('.')]
        
        v1, v2 = normalize(version1), normalize(version2)
        return (v1 > v2) - (v1 < v2)
```

## Android Device Manager

### Archivo: `src/services/android_device_manager.py`

```python
import subprocess
import json
import re
from typing import List, Dict, Optional
from .device_manager import Device, DeviceType, DeviceStatus

class AndroidDeviceManager:
    """Gestor específico para dispositivos Android"""
    
    def __init__(self):
        self.adb_path = self._find_adb()
        self.emulator_path = self._find_emulator()
    
    def discover(self) -> List[Device]:
        """Descubre dispositivos Android"""
        devices = []
        
        # Dispositivos conectados via ADB
        connected = self._get_connected_devices()
        devices.extend(connected)
        
        # Emuladores disponibles
        available_emulators = self._get_available_emulators()
        devices.extend(available_emulators)
        
        return devices
    
    def _get_connected_devices(self) -> List[Device]:
        """Obtiene dispositivos conectados via ADB"""
        devices = []
        
        try:
            result = subprocess.run(
                [self.adb_path, 'devices', '-l'],
                capture_output=True, text=True, check=True
            )
            
            for line in result.stdout.strip().split('\n')[1:]:  # Skip header
                if '\t' in line:
                    device_id, status_info = line.split('\t', 1)
                    
                    if 'device' in status_info:
                        device_info = self._get_device_info(device_id)
                        devices.append(device_info)
                        
        except subprocess.CalledProcessError as e:
            print(f"Error ejecutando adb devices: {e}")
        
        return devices
    
    def _get_device_info(self, device_id: str) -> Device:
        """Obtiene información detallada del dispositivo"""
        # Propiedades del dispositivo
        props = self._get_device_properties(device_id)
        
        # Determinar tipo
        is_emulator = device_id.startswith('emulator-') or 'goldfish' in props.get('ro.hardware', '')
        device_type = DeviceType.ANDROID_EMULATOR if is_emulator else DeviceType.ANDROID_REAL
        
        return Device(
            id=device_id,
            name=props.get('ro.product.model', device_id),
            type=device_type,
            platform='android',
            version=props.get('ro.build.version.release', 'unknown'),
            status=DeviceStatus.AVAILABLE,
            capabilities={
                'screen_size': self._get_screen_size(device_id),
                'api_level': props.get('ro.build.version.sdk', 'unknown'),
                'architecture': props.get('ro.product.cpu.abi', 'unknown'),
                'manufacturer': props.get('ro.product.manufacturer', 'unknown')
            },
            connection_info={
                'adb_id': device_id,
                'type': 'adb'
            }
        )
    
    def _get_device_properties(self, device_id: str) -> Dict[str, str]:
        """Obtiene propiedades del dispositivo"""
        props = {}
        
        try:
            result = subprocess.run(
                [self.adb_path, '-s', device_id, 'shell', 'getprop'],
                capture_output=True, text=True, check=True
            )
            
            for line in result.stdout.strip().split('\n'):
                match = re.match(r'\[([^\]]+)\]: \[([^\]]*)\]', line)
                if match:
                    key, value = match.groups()
                    props[key] = value
                    
        except subprocess.CalledProcessError:
            pass
        
        return props
    
    def _get_screen_size(self, device_id: str) -> str:
        """Obtiene tamaño de pantalla"""
        try:
            result = subprocess.run(
                [self.adb_path, '-s', device_id, 'shell', 'wm', 'size'],
                capture_output=True, text=True, check=True
            )
            
            match = re.search(r'(\d+x\d+)', result.stdout)
            return match.group(1) if match else 'unknown'
            
        except subprocess.CalledProcessError:
            return 'unknown'
    
    def _get_available_emulators(self) -> List[Device]:
        """Obtiene emuladores disponibles (no corriendo)"""
        devices = []
        
        if not self.emulator_path:
            return devices
        
        try:
            result = subprocess.run(
                [self.emulator_path, '-list-avds'],
                capture_output=True, text=True, check=True
            )
            
            running_emulators = {d.id for d in self._get_connected_devices() 
                               if d.type == DeviceType.ANDROID_EMULATOR}
            
            for avd_name in result.stdout.strip().split('\n'):
                if avd_name and avd_name not in running_emulators:
                    avd_info = self._get_avd_info(avd_name)
                    devices.append(avd_info)
                    
        except subprocess.CalledProcessError:
            pass
        
        return devices
    
    def _get_avd_info(self, avd_name: str) -> Device:
        """Obtiene información del AVD"""
        return Device(
            id=f"avd:{avd_name}",
            name=avd_name,
            type=DeviceType.ANDROID_EMULATOR,
            platform='android',
            version='unknown',  # Se obtendría del config.ini del AVD
            status=DeviceStatus.OFFLINE,
            capabilities={
                'avd_name': avd_name,
                'can_start': True
            },
            connection_info={
                'avd_name': avd_name,
                'type': 'avd'
            }
        )
    
    def start_emulator(self, avd_name: str, **kwargs) -> bool:
        """Inicia emulador Android"""
        if not self.emulator_path:
            return False
        
        cmd = [self.emulator_path, '-avd', avd_name]
        
        # Opciones adicionales
        if kwargs.get('headless'):
            cmd.append('-no-window')
        if kwargs.get('wipe_data'):
            cmd.append('-wipe-data')
        
        try:
            subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            return True
        except Exception as e:
            print(f"Error iniciando emulador {avd_name}: {e}")
            return False
    
    def stop_emulator(self, device_id: str) -> bool:
        """Detiene emulador"""
        try:
            subprocess.run(
                [self.adb_path, '-s', device_id, 'emu', 'kill'],
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def install_apk(self, device_id: str, apk_path: str) -> bool:
        """Instala APK en dispositivo"""
        try:
            subprocess.run(
                [self.adb_path, '-s', device_id, 'install', '-r', apk_path],
                check=True
            )
            return True
        except subprocess.CalledProcessError as e:
            print(f"Error instalando APK: {e}")
            return False
    
    def uninstall_app(self, device_id: str, package_name: str) -> bool:
        """Desinstala app del dispositivo"""
        try:
            subprocess.run(
                [self.adb_path, '-s', device_id, 'uninstall', package_name],
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def _find_adb(self) -> str:
        """Encuentra ejecutable ADB"""
        # Buscar en PATH
        try:
            result = subprocess.run(['which', 'adb'], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        
        # Buscar en ubicaciones comunes
        common_paths = [
            '/usr/local/bin/adb',
            '~/Library/Android/sdk/platform-tools/adb',
            '~/Android/Sdk/platform-tools/adb'
        ]
        
        for path in common_paths:
            expanded = os.path.expanduser(path)
            if os.path.exists(expanded):
                return expanded
        
        raise RuntimeError("ADB no encontrado. Instalar Android SDK.")
    
    def _find_emulator(self) -> Optional[str]:
        """Encuentra ejecutable emulator"""
        try:
            result = subprocess.run(['which', 'emulator'], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        
        # Buscar en ubicaciones comunes
        common_paths = [
            '~/Library/Android/sdk/emulator/emulator',
            '~/Android/Sdk/emulator/emulator'
        ]
        
        for path in common_paths:
            expanded = os.path.expanduser(path)
            if os.path.exists(expanded):
                return expanded
        
        return None
```

## iOS Device Manager

### Archivo: `src/services/ios_device_manager.py`

```python
import subprocess
import json
import plistlib
from typing import List, Dict, Optional
from .device_manager import Device, DeviceType, DeviceStatus

class IOSDeviceManager:
    """Gestor específico para dispositivos iOS (solo macOS)"""
    
    def __init__(self):
        self.xcrun_path = '/usr/bin/xcrun'
        self.simctl_available = self._check_simctl()
    
    def discover(self) -> List[Device]:
        """Descubre dispositivos iOS"""
        devices = []
        
        if not self.simctl_available:
            return devices
        
        # Simuladores
        simulators = self._get_simulators()
        devices.extend(simulators)
        
        # Dispositivos reales (requiere configuración adicional)
        real_devices = self._get_real_devices()
        devices.extend(real_devices)
        
        return devices
    
    def _get_simulators(self) -> List[Device]:
        """Obtiene simuladores iOS"""
        devices = []
        
        try:
            result = subprocess.run(
                [self.xcrun_path, 'simctl', 'list', 'devices', '--json'],
                capture_output=True, text=True, check=True
            )
            
            data = json.loads(result.stdout)
            
            for runtime, device_list in data['devices'].items():
                if 'iOS' not in runtime:
                    continue
                
                ios_version = self._extract_ios_version(runtime)
                
                for device_info in device_list:
                    if device_info['isAvailable']:
                        device = Device(
                            id=device_info['udid'],
                            name=device_info['name'],
                            type=DeviceType.IOS_SIMULATOR,
                            platform='ios',
                            version=ios_version,
                            status=self._get_simulator_status(device_info['state']),
                            capabilities={
                                'runtime': runtime,
                                'device_type': device_info.get('deviceTypeIdentifier', ''),
                                'can_start': device_info['state'] != 'Booted'
                            },
                            connection_info={
                                'udid': device_info['udid'],
                                'type': 'simulator'
                            }
                        )
                        devices.append(device)
                        
        except (subprocess.CalledProcessError, json.JSONDecodeError) as e:
            print(f"Error obteniendo simuladores iOS: {e}")
        
        return devices
    
    def _get_real_devices(self) -> List[Device]:
        """Obtiene dispositivos iOS reales"""
        devices = []
        
        try:
            # Usar ios-deploy o libimobiledevice si están disponibles
            result = subprocess.run(
                ['idevice_id', '-l'],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                for udid in result.stdout.strip().split('\n'):
                    if udid:
                        device_info = self._get_real_device_info(udid)
                        if device_info:
                            devices.append(device_info)
                            
        except FileNotFoundError:
            # libimobiledevice no instalado
            pass
        except subprocess.CalledProcessError:
            pass
        
        return devices
    
    def _get_real_device_info(self, udid: str) -> Optional[Device]:
        """Obtiene información de dispositivo real"""
        try:
            result = subprocess.run(
                ['ideviceinfo', '-u', udid],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                info = {}
                for line in result.stdout.strip().split('\n'):
                    if ': ' in line:
                        key, value = line.split(': ', 1)
                        info[key] = value
                
                return Device(
                    id=udid,
                    name=info.get('DeviceName', 'iOS Device'),
                    type=DeviceType.IOS_REAL,
                    platform='ios',
                    version=info.get('ProductVersion', 'unknown'),
                    status=DeviceStatus.AVAILABLE,
                    capabilities={
                        'model': info.get('ProductType', ''),
                        'architecture': info.get('CPUArchitecture', ''),
                        'device_class': info.get('DeviceClass', '')
                    },
                    connection_info={
                        'udid': udid,
                        'type': 'real_device'
                    }
                )
                
        except subprocess.CalledProcessError:
            pass
        
        return None
    
    def start_simulator(self, udid: str) -> bool:
        """Inicia simulador iOS"""
        try:
            subprocess.run(
                [self.xcrun_path, 'simctl', 'boot', udid],
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def stop_simulator(self, udid: str) -> bool:
        """Detiene simulador iOS"""
        try:
            subprocess.run(
                [self.xcrun_path, 'simctl', 'shutdown', udid],
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def install_app(self, udid: str, app_path: str) -> bool:
        """Instala app en simulador"""
        try:
            subprocess.run(
                [self.xcrun_path, 'simctl', 'install', udid, app_path],
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def _check_simctl(self) -> bool:
        """Verifica si simctl está disponible"""
        try:
            subprocess.run(
                [self.xcrun_path, 'simctl', 'help'],
                capture_output=True, check=True
            )
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _extract_ios_version(self, runtime: str) -> str:
        """Extrae versión iOS del runtime"""
        import re
        match = re.search(r'iOS[\s-]+(\d+[\.-]\d+)', runtime)
        return match.group(1).replace('-', '.') if match else 'unknown'
    
    def _get_simulator_status(self, state: str) -> DeviceStatus:
        """Convierte estado de simulador a DeviceStatus"""
        mapping = {
            'Shutdown': DeviceStatus.AVAILABLE,
            'Booted': DeviceStatus.BUSY,
            'Booting': DeviceStatus.BOOTING
        }
        return mapping.get(state, DeviceStatus.OFFLINE)
```

## Cloud Device Manager

### Archivo: `src/services/cloud_device_manager.py`

```python
from typing import List, Dict, Optional
from .device_manager import Device, DeviceType, DeviceStatus

class CloudDeviceManager:
    """Gestor para dispositivos en la nube (BrowserStack, Sauce Labs, etc.)"""
    
    def __init__(self):
        self.providers = {
            'browserstack': BrowserStackProvider(),
            'saucelabs': SauceLabsProvider(),
            'aws_device_farm': AWSDeviceFarmProvider()
        }
    
    def discover(self) -> List[Device]:
        """Descubre dispositivos en la nube"""
        devices = []
        
        for provider_name, provider in self.providers.items():
            if provider.is_configured():
                try:
                    provider_devices = provider.get_devices()
                    devices.extend(provider_devices)
                except Exception as e:
                    print(f"Error obteniendo dispositivos de {provider_name}: {e}")
        
        return devices

class BrowserStackProvider:
    """Proveedor BrowserStack"""
    
    def is_configured(self) -> bool:
        """Verifica si está configurado"""
        # Verificar variables de entorno o config
        return False  # Implementar según configuración
    
    def get_devices(self) -> List[Device]:
        """Obtiene dispositivos BrowserStack"""
        # Implementar API de BrowserStack
        return []

class SauceLabsProvider:
    """Proveedor Sauce Labs"""
    
    def is_configured(self) -> bool:
        return False
    
    def get_devices(self) -> List[Device]:
        return []

class AWSDeviceFarmProvider:
    """Proveedor AWS Device Farm"""
    
    def is_configured(self) -> bool:
        return False
    
    def get_devices(self) -> List[Device]:
        return []
```

## Integración con QAK

### API Routes: `src/api/mobile_device_routes.py`

```python
from flask import Blueprint, jsonify, request
from src.services.device_manager import DeviceManager

mobile_device_bp = Blueprint('mobile_devices', __name__)
device_manager = DeviceManager()

@mobile_device_bp.route('/devices', methods=['GET'])
def list_devices():
    """Lista todos los dispositivos"""
    platform = request.args.get('platform')
    devices = device_manager.discover_devices()
    
    if platform:
        devices = [d for d in devices if d.platform == platform.lower()]
    
    return jsonify({
        'devices': [{
            'id': d.id,
            'name': d.name,
            'type': d.type.value,
            'platform': d.platform,
            'version': d.version,
            'status': d.status.value,
            'capabilities': d.capabilities
        } for d in devices]
    })

@mobile_device_bp.route('/devices/available', methods=['GET'])
def available_devices():
    """Lista dispositivos disponibles"""
    platform = request.args.get('platform')
    devices = device_manager.get_available_devices(platform)
    
    return jsonify({
        'devices': [{
            'id': d.id,
            'name': d.name,
            'type': d.type.value,
            'platform': d.platform,
            'version': d.version
        } for d in devices]
    })

@mobile_device_bp.route('/devices/best', methods=['POST'])
def get_best_device():
    """Obtiene el mejor dispositivo según requisitos"""
    requirements = request.json or {}
    device = device_manager.get_best_device(requirements)
    
    if device:
        return jsonify({
            'device': {
                'id': device.id,
                'name': device.name,
                'type': device.type.value,
                'platform': device.platform,
                'version': device.version
            }
        })
    else:
        return jsonify({'error': 'No hay dispositivos disponibles'}), 404
```

## Configuración y Setup

### Script de configuración: `scripts/setup_mobile_testing.py`

```python
#!/usr/bin/env python3
"""
Script de configuración para testing móvil en QAK
"""

import subprocess
import sys
import os
from pathlib import Path

def check_android_sdk():
    """Verifica instalación de Android SDK"""
    android_home = os.environ.get('ANDROID_HOME')
    if not android_home or not os.path.exists(android_home):
        print("❌ Android SDK no encontrado")
        print("Instalar Android Studio y configurar ANDROID_HOME")
        return False
    
    adb_path = os.path.join(android_home, 'platform-tools', 'adb')
    if not os.path.exists(adb_path):
        print("❌ ADB no encontrado")
        return False
    
    print("✅ Android SDK configurado")
    return True

def check_ios_tools():
    """Verifica herramientas iOS (solo macOS)"""
    if sys.platform != 'darwin':
        print("ℹ️ Herramientas iOS solo disponibles en macOS")
        return True
    
    try:
        subprocess.run(['xcrun', 'simctl', 'help'], 
                      capture_output=True, check=True)
        print("✅ Xcode/simctl disponible")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Xcode no encontrado")
        return False

def install_appium():
    """Instala Appium"""
    try:
        subprocess.run(['npm', 'install', '-g', 'appium'], check=True)
        subprocess.run(['appium', 'driver', 'install', 'uiautomator2'], check=True)
        if sys.platform == 'darwin':
            subprocess.run(['appium', 'driver', 'install', 'xcuitest'], check=True)
        print("✅ Appium instalado")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error instalando Appium")
        return False

def create_sample_avd():
    """Crea AVD de ejemplo"""
    try:
        # Crear AVD básico para testing
        subprocess.run([
            'avdmanager', 'create', 'avd',
            '-n', 'QAK_Test_Device',
            '-k', 'system-images;android-30;google_apis;x86_64',
            '--force'
        ], check=True)
        print("✅ AVD de prueba creado")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ No se pudo crear AVD (opcional)")
        return False

def main():
    print("🚀 Configurando QAK Mobile Testing...\n")
    
    checks = [
        ("Android SDK", check_android_sdk),
        ("iOS Tools", check_ios_tools),
        ("Appium", install_appium),
        ("Sample AVD", create_sample_avd)
    ]
    
    results = []
    for name, check_func in checks:
        print(f"Verificando {name}...")
        result = check_func()
        results.append((name, result))
        print()
    
    print("📋 Resumen:")
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    if all(result for _, result in results[:2]):  # SDK y herramientas básicas
        print("\n🎉 Configuración básica completada")
        print("Ejecutar: python -m src.services.device_manager para probar")
    else:
        print("\n⚠️ Configuración incompleta")
        print("Revisar errores arriba")

if __name__ == '__main__':
    main()
```

## Próximos Pasos

1. **Health Monitoring**: Sistema de monitoreo de dispositivos
2. **Auto-healing**: Recuperación automática de dispositivos con problemas
3. **Load Balancing**: Distribución de tests entre múltiples dispositivos
4. **Cloud Integration**: Integración completa con proveedores cloud

Ver `04_mobile_prompts.md` para adaptación de prompts AI para móvil.