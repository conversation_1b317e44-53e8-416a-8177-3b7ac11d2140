/**
 * Utilidades para formatear y procesar eventos de aery-browser
 */

/**
 * Formatear timestamp a formato legible
 */
export function formatTimestamp(timestamp: string | undefined): string {
  if (!timestamp) {
    return 'Fecha no disponible';
  }

  try {
    const date = new Date(timestamp);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Fecha inválida';
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // Si es hoy
    if (diffDays === 0) {
      if (diffMinutes < 1) {
        return 'Hace un momento';
      } else if (diffMinutes < 60) {
        return `Hace ${diffMinutes} min`;
      } else {
        return `Hace ${diffHours}h`;
      }
    }

    // Si es ayer
    if (diffDays === 1) {
      return `Ayer ${date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Si es esta semana
    if (diffDays < 7) {
      return `${diffDays} días - ${date.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Fecha completa
    return date.toLocaleString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.warn('Error formatting timestamp:', timestamp, error);
    return 'Fecha inválida';
  }
}

/**
 * Formatear tipo de evento a texto legible
 */
export function formatEventType(type: string | undefined): string {
  if (!type) {
    return 'Evento Desconocido';
  }

  const typeMap: Record<string, string> = {
    'SessionStartEvent': 'Inicio de Sesión',
    'TaskCreateEvent': 'Crear Tarea',
    'CreateAgentStepEvent': 'Paso del Agente',
    'CreateAgentSessionEvent': 'Inicio de Sesión',
    'CreateAgentTaskEvent': 'Crear Tarea',
    'OutputFileEvent': 'Archivo de Salida',
    'UpdateTaskEvent': 'Actualizar Tarea',
    'session': 'Sesión',
    'task': 'Tarea',
    'step': 'Paso',
    'output_file': 'Archivo',
    'update_task': 'Actualización',
    'session_started': 'Sesión Iniciada',
    'task_started': 'Tarea Iniciada',
    'step_completed': 'Paso Completado',
    'error_occurred': 'Error',
    'file_created': 'Archivo Creado'
  };

  return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
}

/**
 * Obtener icono para tipo de evento
 */
export function getEventIcon(type: string | undefined): string {
  if (!type) {
    return '📊';
  }

  const iconMap: Record<string, string> = {
    'SessionStartEvent': '🚀',
    'TaskCreateEvent': '📋',
    'CreateAgentStepEvent': '👆',
    'CreateAgentSessionEvent': '🚀',
    'CreateAgentTaskEvent': '📋',
    'OutputFileEvent': '📄',
    'UpdateTaskEvent': '🔄',
    'session': '🖥️',
    'task': '📋',
    'step': '👆',
    'output_file': '📄',
    'update_task': '🔄',
    'session_started': '🚀',
    'task_started': '▶️',
    'step_completed': '✅',
    'error_occurred': '❌',
    'file_created': '📝'
  };

  return iconMap[type] || '📊';
}

/**
 * Obtener color para tipo de evento
 */
export function getEventColor(type: string | undefined): string {
  if (!type) {
    return 'bg-gray-100 text-gray-800 border-gray-200';
  }

  const colorMap: Record<string, string> = {
    'SessionStartEvent': 'bg-blue-100 text-blue-800 border-blue-200',
    'TaskCreateEvent': 'bg-green-100 text-green-800 border-green-200',
    'CreateAgentStepEvent': 'bg-purple-100 text-purple-800 border-purple-200',
    'CreateAgentSessionEvent': 'bg-blue-100 text-blue-800 border-blue-200',
    'CreateAgentTaskEvent': 'bg-green-100 text-green-800 border-green-200',
    'OutputFileEvent': 'bg-orange-100 text-orange-800 border-orange-200',
    'UpdateTaskEvent': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'session': 'bg-blue-100 text-blue-800 border-blue-200',
    'task': 'bg-green-100 text-green-800 border-green-200',
    'step': 'bg-purple-100 text-purple-800 border-purple-200',
    'output_file': 'bg-orange-100 text-orange-800 border-orange-200',
    'update_task': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'session_started': 'bg-blue-100 text-blue-800 border-blue-200',
    'task_started': 'bg-green-100 text-green-800 border-green-200',
    'step_completed': 'bg-purple-100 text-purple-800 border-purple-200',
    'error_occurred': 'bg-red-100 text-red-800 border-red-200',
    'file_created': 'bg-orange-100 text-orange-800 border-orange-200'
  };

  return colorMap[type] || 'bg-gray-100 text-gray-800 border-gray-200';
}

/**
 * Formatear nombre de usuario para mostrar
 */
export function formatUserDisplayName(userId: string | undefined): string {
  if (!userId) {
    return 'Usuario Desconocido';
  }

  // Usuario temporal de aery-browser
  if (userId === '*************-9999-9999-************') {
    return 'Usuario Temporal';
  }

  // Usuarios del sistema
  if (userId.startsWith('sys_')) {
    const systemUser = userId.substring(4);
    return `Sistema (${systemUser})`;
  }

  // Usuarios invitados
  if (userId.startsWith('guest_')) {
    const guestId = userId.substring(6);
    return `Invitado (${guestId})`;
  }

  // Usuarios autenticados
  if (userId.startsWith('auth_user_')) {
    const authId = userId.substring(10);
    return `Usuario Auth (${authId})`;
  }

  // Usuarios generados
  if (userId.startsWith('user_')) {
    const generatedId = userId.substring(5);
    return `Usuario (${generatedId})`;
  }

  // UUIDs largos - mostrar versión corta
  if (userId.length > 20 && userId.includes('-')) {
    return `Usuario (${userId.substring(0, 8)}...)`;
  }

  // Emails
  if (userId.includes('@')) {
    return userId;
  }

  // IDs cortos
  if (userId.length <= 12) {
    return `Usuario (${userId})`;
  }

  // Fallback para IDs largos
  return `Usuario (${userId.substring(0, 8)}...)`;
}

/**
 * Obtener color para usuario basado en su ID
 */
export function getUserColor(userId: string | undefined): string {
  if (!userId) {
    return 'bg-gray-100 text-gray-800';
  }

  // Usuario temporal
  if (userId === '*************-9999-9999-************') {
    return 'bg-gray-100 text-gray-600';
  }

  // Usuarios del sistema
  if (userId.startsWith('sys_')) {
    return 'bg-blue-100 text-blue-800';
  }

  // Usuarios invitados
  if (userId.startsWith('guest_')) {
    return 'bg-yellow-100 text-yellow-800';
  }

  // Usuarios autenticados
  if (userId.startsWith('auth_user_')) {
    return 'bg-green-100 text-green-800';
  }

  // Usuarios generados
  if (userId.startsWith('user_')) {
    return 'bg-purple-100 text-purple-800';
  }

  // Emails
  if (userId.includes('@')) {
    return 'bg-indigo-100 text-indigo-800';
  }

  // Fallback
  return 'bg-slate-100 text-slate-800';
}

/**
 * Formatear device ID para mostrar
 */
export function formatDeviceDisplayName(deviceId: string | undefined): string {
  if (!deviceId) {
    return 'Dispositivo Desconocido';
  }

  // UUIDs largos - mostrar versión corta
  if (deviceId.length > 20 && deviceId.includes('-')) {
    return `Dispositivo (${deviceId.substring(0, 8)}...)`;
  }

  // IDs cortos
  if (deviceId.length <= 12) {
    return `Dispositivo (${deviceId})`;
  }

  // Fallback para IDs largos
  return `Dispositivo (${deviceId.substring(0, 8)}...)`;
}

/**
 * Extraer información contextual del evento
 */
export function getEventContext(eventData: any): {
  action?: string;
  target?: string;
  details?: string;
} {
  if (!eventData) return {};

  // Para eventos de step
  if (eventData.action_type) {
    return {
      action: eventData.action_type,
      target: eventData.coordinate || eventData.text || eventData.url,
      details: eventData.reasoning
    };
  }

  // Para eventos de task
  if (eventData.task) {
    return {
      action: 'Tarea',
      target: eventData.task,
      details: eventData.description
    };
  }

  // Para eventos de session
  if (eventData.viewport) {
    return {
      action: 'Sesión',
      target: `${eventData.viewport.width}x${eventData.viewport.height}`,
      details: eventData.user_agent
    };
  }

  // Para archivos
  if (eventData.file_path) {
    return {
      action: 'Archivo',
      target: eventData.file_path,
      details: eventData.content_type
    };
  }

  return {};
}

/**
 * Filtrar eventos basado en criterios
 */
export function filterEvents(
  events: any[],
  filters: {
    searchTerm?: string;
    type?: string;
    userId?: string;
    dateRange?: { start: Date; end: Date };
  }
): any[] {
  return events.filter(event => {
    // Filtro de búsqueda
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      const searchableText = [
        event.id,
        event.type,
        event.user_id,
        event.device_id,
        JSON.stringify(event.data)
      ].join(' ').toLowerCase();

      if (!searchableText.includes(searchLower)) {
        return false;
      }
    }

    // Filtro de tipo
    if (filters.type && filters.type !== 'all' && event.type !== filters.type) {
      return false;
    }

    // Filtro de usuario
    if (filters.userId && filters.userId !== 'all' && event.user_id !== filters.userId) {
      return false;
    }

    // Filtro de fecha
    if (filters.dateRange) {
      const eventDate = new Date(event.timestamp);
      if (eventDate < filters.dateRange.start || eventDate > filters.dateRange.end) {
        return false;
      }
    }

    return true;
  });
}