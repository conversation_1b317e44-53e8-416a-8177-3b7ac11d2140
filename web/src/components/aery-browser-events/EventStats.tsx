"use client";

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AeryBrowserEvent } from '@/types/aery-browser-events';
import { Activity, Clock, Database, Monitor, TrendingUp, Users } from 'lucide-react';

interface EventStatsProps {
  events: AeryBrowserEvent[];
}

export function EventStats({ events }: EventStatsProps) {
  // Calculate statistics
  const totalEvents = events.length;
  const uniqueUsers = new Set(events.filter(e => e.data?.user_id).map(e => e.data.user_id)).size;
  const uniqueDevices = new Set(events.filter(e => e.data?.device_id).map(e => e.data.device_id)).size;

  // Events by type
  const eventsByType = events.reduce((acc, event) => {
    acc[event.event_type] = (acc[event.event_type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Recent events (last 5 minutes)
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  const recentEvents = events.filter(e => e.data?.created_at && new Date(e.data.created_at) > fiveMinutesAgo).length;

  // Events per hour (last 24 hours)
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  const last24HourEvents = events.filter(e => e.data?.created_at && new Date(e.data.created_at) > twentyFourHoursAgo);
  const eventsPerHour = last24HourEvents.length > 0 ? (last24HourEvents.length / 24).toFixed(1) : '0';

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'session': return 'bg-blue-100 text-blue-800';
      case 'task': return 'bg-green-100 text-green-800';
      case 'step': return 'bg-yellow-100 text-yellow-800';
      case 'output_file': return 'bg-purple-100 text-purple-800';
      case 'update_task': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'session': return <Monitor className="h-4 w-4" />;
      case 'task': return <Activity className="h-4 w-4" />;
      case 'step': return <TrendingUp className="h-4 w-4" />;
      case 'output_file': return <Database className="h-4 w-4" />;
      case 'update_task': return <Clock className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Total Events */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Eventos</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalEvents}</div>
          <p className="text-xs text-muted-foreground">
            {recentEvents} en los últimos 5 min
          </p>
        </CardContent>
      </Card>

      {/* Unique Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Usuarios Únicos</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{uniqueUsers}</div>
          <p className="text-xs text-muted-foreground">
            Usuarios activos
          </p>
        </CardContent>
      </Card>

      {/* Unique Devices */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Dispositivos</CardTitle>
          <Monitor className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{uniqueDevices}</div>
          <p className="text-xs text-muted-foreground">
            Dispositivos únicos
          </p>
        </CardContent>
      </Card>

      {/* Events per Hour */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Eventos/Hora</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{eventsPerHour}</div>
          <p className="text-xs text-muted-foreground">
            Promedio últimas 24h
          </p>
        </CardContent>
      </Card>

      {/* Events by Type */}
      {Object.keys(eventsByType).length > 0 && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="text-sm font-medium">Eventos por Tipo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {Object.entries(eventsByType).map(([type, count], index) => (
                <div key={`event-type-${type}-${count}-${index}`} className="flex items-center gap-2">
                  <Badge className={getEventTypeColor(type)} variant="secondary">
                    <span className="flex items-center gap-1">
                      {getEventTypeIcon(type)}
                      {type}
                    </span>
                  </Badge>
                  <span className="text-sm font-medium">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}