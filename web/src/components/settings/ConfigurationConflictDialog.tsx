"use client";

import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>gle, Setting<PERSON> } from "lucide-react";

interface ConflictInfo {
  config_id: string;
  config_name: string;
  config_type: string;
  conflicting_execution_types: string[];
  is_predefined: boolean;
}

interface ConfigurationConflictDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  conflicts: ConflictInfo[];
  executionTypes: string[];
  onResolve: (action: 'remove_from_existing' | 'cancel') => void;
  isLoading?: boolean;
}

const executionTypeLabels: Record<string, string> = {
  smoke: "Smoke Tests",
  full: "Full Tests", 
  case: "Test Cases",
  suite: "Test Suites",
  codegen: "Code Generation"
};

const executionTypeColors: Record<string, string> = {
  smoke: "bg-yellow-100 text-yellow-800",
  full: "bg-blue-100 text-blue-800",
  case: "bg-green-100 text-green-800", 
  suite: "bg-purple-100 text-purple-800",
  codegen: "bg-orange-100 text-orange-800"
};

export function ConfigurationConflictDialog({
  open,
  onOpenChange,
  conflicts,
  executionTypes,
  onResolve,
  isLoading = false
}: ConfigurationConflictDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Execution Type Conflicts Detected
          </DialogTitle>
          <DialogDescription>
            The execution types you selected are already assigned to other configurations. 
            You need to resolve these conflicts before proceeding.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Execution Types Being Assigned:</h4>
            <div className="flex flex-wrap gap-2">
              {executionTypes.map((type) => (
                <Badge 
                  key={type} 
                  variant="outline"
                  className={executionTypeColors[type] || "bg-gray-100 text-gray-800"}
                >
                  {executionTypeLabels[type] || type}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Conflicting Configurations:</h4>
            <div className="space-y-3">
              {conflicts.map((conflict) => (
                <div 
                  key={conflict.config_id} 
                  className="border rounded-lg p-3 bg-red-50 border-red-200"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4 text-gray-600" />
                      <span className="font-medium">{conflict.config_name}</span>
                      <Badge variant={conflict.is_predefined ? "default" : "secondary"}>
                        {conflict.is_predefined ? "Predefined" : "Custom"}
                      </Badge>
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-sm text-gray-600">Conflicting execution types: </span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {conflict.conflicting_execution_types.map((type) => (
                        <Badge 
                          key={type} 
                          variant="destructive" 
                          className="text-xs"
                        >
                          {executionTypeLabels[type] || type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h4 className="font-medium text-blue-900 mb-1">Resolution Options:</h4>
            <p className="text-sm text-blue-800">
              <strong>Remove from existing:</strong> The conflicting execution types will be removed 
              from the existing configurations and assigned to this configuration.
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            variant="outline" 
            onClick={() => onResolve('cancel')}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={() => onResolve('remove_from_existing')}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? "Resolving..." : "Remove from Existing & Continue"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
