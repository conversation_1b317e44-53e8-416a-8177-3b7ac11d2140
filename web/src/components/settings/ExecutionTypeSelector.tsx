"use client";

import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ExecutionTypeSelectorProps {
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
}

const executionTypes = [
  {
    id: "smoke",
    label: "Smoke Tests",
    description: "Quick tests to verify basic functionality",
    color: "bg-yellow-100 text-yellow-800 border-yellow-300",
    icon: "🔥"
  },
  {
    id: "full", 
    label: "Full Tests",
    description: "Comprehensive testing with complete coverage",
    color: "bg-blue-100 text-blue-800 border-blue-300",
    icon: "🔍"
  },
  {
    id: "case",
    label: "Test Cases", 
    description: "Individual test case execution",
    color: "bg-green-100 text-green-800 border-green-300",
    icon: "📝"
  },
  {
    id: "suite",
    label: "Test Suites",
    description: "Batch execution of multiple test cases",
    color: "bg-purple-100 text-purple-800 border-purple-300", 
    icon: "📚"
  },
  {
    id: "codegen",
    label: "Code Generation",
    description: "Automated code generation from tests",
    color: "bg-orange-100 text-orange-800 border-orange-300",
    icon: "⚡"
  }
];

export function ExecutionTypeSelector({ value, onChange, disabled = false }: ExecutionTypeSelectorProps) {
  const handleToggle = (typeId: string) => {
    if (disabled) return;
    
    const newValue = value.includes(typeId)
      ? value.filter(v => v !== typeId)
      : [...value, typeId];
    
    onChange(newValue);
  };

  return (
    <TooltipProvider>
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">Execution Types</h4>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="h-4 w-4 text-gray-400" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">
                Select which types of test execution can use this configuration.
                Each execution type should typically have only one default configuration.
              </p>
            </TooltipContent>
          </Tooltip>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {executionTypes.map((type) => {
            const isSelected = value.includes(type.id);
            
            return (
              <div
                key={type.id}
                className={`
                  relative border rounded-lg p-3 cursor-pointer transition-all
                  ${isSelected 
                    ? 'border-blue-300 bg-blue-50 shadow-sm' 
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }
                  ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                `}
                onClick={() => handleToggle(type.id)}
              >
                <div className="flex items-start gap-3">
                  <Checkbox
                    checked={isSelected}
                    onChange={() => handleToggle(type.id)}
                    disabled={disabled}
                    className="mt-0.5"
                  />
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-lg">{type.icon}</span>
                      <span className="font-medium text-sm">{type.label}</span>
                    </div>
                    
                    <p className="text-xs text-gray-600 mb-2">
                      {type.description}
                    </p>
                    
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${type.color}`}
                    >
                      {type.id}
                    </Badge>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {value.length === 0 && (
          <p className="text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded p-2">
            ⚠️ Select at least one execution type for this configuration to be useful.
          </p>
        )}
        
        <div className="text-xs text-gray-500">
          Selected: {value.length} of {executionTypes.length} execution types
        </div>
      </div>
    </TooltipProvider>
  );
}
