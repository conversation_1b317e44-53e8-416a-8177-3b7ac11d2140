"use client";

import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ConfigurationFormFieldsProps {
  form: UseFormReturn<any>;
}

const FieldWithTooltip = ({ 
  children, 
  tooltip 
}: { 
  children: React.ReactNode; 
  tooltip: string;
}) => (
  <TooltipProvider>
    <div className="flex items-center gap-2">
      {children}
      <Tooltip>
        <TooltipTrigger>
          <HelpCircle className="h-4 w-4 text-gray-400" />
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </div>
  </TooltipProvider>
);

export function ConfigurationFormFields({ form }: ConfigurationFormFieldsProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>
        
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Configuration Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter configuration name" {...field} />
              </FormControl>
              <FormDescription>
                A descriptive name for this configuration
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Describe when and how this configuration should be used"
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Optional description explaining the purpose of this configuration
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Browser Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Browser Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="headless"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <FieldWithTooltip tooltip="Run browser without GUI. Faster but you can't see what's happening.">
                    <FormLabel>Headless Mode</FormLabel>
                  </FieldWithTooltip>
                  <FormDescription className="text-xs">
                    Run browser in background
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="use_vision"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <FieldWithTooltip tooltip="Enable AI vision capabilities to understand page content visually.">
                    <FormLabel>Use Vision</FormLabel>
                  </FieldWithTooltip>
                  <FormDescription className="text-xs">
                    AI visual understanding
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="highlight_elements"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <FieldWithTooltip tooltip="Highlight elements that the AI is interacting with.">
                    <FormLabel>Highlight Elements</FormLabel>
                  </FieldWithTooltip>
                  <FormDescription className="text-xs">
                    Visual element highlighting
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="generate_gif"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <FieldWithTooltip tooltip="Generate animated GIFs of test execution for documentation.">
                    <FormLabel>Generate GIF</FormLabel>
                  </FieldWithTooltip>
                  <FormDescription className="text-xs">
                    Create execution recordings
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Performance Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Performance & Timing</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="max_steps"
            render={({ field }) => (
              <FormItem>
                <FieldWithTooltip tooltip="Maximum number of actions the AI can take before stopping.">
                  <FormLabel>Max Steps</FormLabel>
                </FieldWithTooltip>
                <FormControl>
                  <Input 
                    type="number" 
                    min="1" 
                    max="500"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormDescription>
                  Limit AI actions (1-500)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="max_failures"
            render={({ field }) => (
              <FormItem>
                <FieldWithTooltip tooltip="How many failures to tolerate before giving up.">
                  <FormLabel>Max Failures</FormLabel>
                </FieldWithTooltip>
                <FormControl>
                  <Input 
                    type="number" 
                    min="1" 
                    max="10"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormDescription>
                  Failure tolerance (1-10)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="wait_between_actions"
            render={({ field }) => (
              <FormItem>
                <FieldWithTooltip tooltip="Pause between actions to ensure page stability.">
                  <FormLabel>Wait Between Actions (seconds)</FormLabel>
                </FieldWithTooltip>
                <FormControl>
                  <Input 
                    type="number" 
                    min="0" 
                    max="10" 
                    step="0.1"
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  />
                </FormControl>
                <FormDescription>
                  Delay between actions (0-10s)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="maximum_wait_page_load_time"
            render={({ field }) => (
              <FormItem>
                <FieldWithTooltip tooltip="Maximum time to wait for pages to load completely.">
                  <FormLabel>Max Page Load Time (seconds)</FormLabel>
                </FieldWithTooltip>
                <FormControl>
                  <Input 
                    type="number" 
                    min="1" 
                    max="120" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormDescription>
                  Page load timeout (1-120s)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* AI Model Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">AI Model Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="model_provider"
            render={({ field }) => (
              <FormItem>
                <FieldWithTooltip tooltip="AI model provider for test execution.">
                  <FormLabel>Model Provider</FormLabel>
                </FieldWithTooltip>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="openrouter">OpenRouter</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="gemini">Google Gemini</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  AI service provider
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="temperature"
            render={({ field }) => (
              <FormItem>
                <FieldWithTooltip tooltip="Controls AI creativity. Lower = more deterministic, Higher = more creative.">
                  <FormLabel>Temperature</FormLabel>
                </FieldWithTooltip>
                <FormControl>
                  <Input 
                    type="number" 
                    min="0" 
                    max="2" 
                    step="0.1"
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  />
                </FormControl>
                <FormDescription>
                  AI creativity level (0-2)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
}
