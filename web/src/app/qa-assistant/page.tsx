"use client";

import { <PERSON><PERSON><PERSON>Editor } from "@/components/execution/GherkinEditor";
import { ManualTestCasesTable } from "@/components/execution/ManualTestCasesTable";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  callEnhanceUser<PERSON>tory,
  callGenerateGher<PERSON>,
  callGenerateManualTestCases,
  createTestCase,
  executeTest,
  getProjects,
  getSuitesByProjectId,
  pauseExecution,
  resumeExecution,
  saveTestHistory,
  stopExecution,
} from "@/lib/api";
import {
  ExecutionResponse as ExecutionResponseV2,
  ExecutionStatus,
  ExecutionTypeEnum,
  FullTestRequest,
  Project,
  SmokeTestRequest,
  TestCaseObject,
  TestSuite
} from "@/lib/types";
import { cn } from "@/lib/utils";
import {
  Bot,
  Check,
  ChevronLeft,
  ChevronRight,
  FileText,
  Flame,
  Info,
  Lightbulb,
  ListChecks,
  Play,
  Save,
  Sparkles
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

type Step = "userStory" | "manualTests" | "gherkin" | "summary";

export default function QaAssistantPage() {
  const { toast } = useToast();

  const [currentStep, setCurrentStep] = useState<Step>("userStory");

  const [originalUserStory, setOriginalUserStory] = useState("");
  const [enhancedUserStory, setEnhancedUserStory] = useState<string | null>(null);
  const [finalUserStory, setFinalUserStory] = useState("");
  const [targetUrl, setTargetUrl] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState<string>("es");

  const [generatedManualTestCases, setGeneratedManualTestCases] = useState<(string | TestCaseObject)[] | null>(null);
  const [finalManualTestCases, setFinalManualTestCases] = useState<(string | TestCaseObject)[]>([]);

  const [gherkinInput, setGherkinInput] = useState("");
  const [generatedGherkin, setGeneratedGherkin] = useState<string | null>(null);
  const [finalGherkin, setFinalGherkin] = useState("");

  const [isLoadingEnhance, setIsLoadingEnhance] = useState(false);
  const [isLoadingManualCases, setIsLoadingManualCases] = useState(false);
  const [isLoadingGherkin, setIsLoadingGherkin] = useState(false);
  const [isLoadingExecution, setIsLoadingExecution] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testExecutionResult, setTestExecutionResult] = useState<any>(null);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [currentExecutionStatus, setCurrentExecutionStatus] = useState<ExecutionStatus | null>(null);

  // Project saving states
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [suites, setSuites] = useState<TestSuite[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [isLoadingSuites, setIsLoadingSuites] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [selectedSuiteId, setSelectedSuiteId] = useState<string>("");
  const [testName, setTestName] = useState("");
  const [testDescription, setTestDescription] = useState("");

  useEffect(() => {
    // Fetch projects when the dialog is shown
    if (showSaveDialog) {
      fetchProjects();
    }
  }, [showSaveDialog]);

  useEffect(() => {
    // Fetch suites when a project is selected
    if (selectedProjectId) {
      fetchSuitesByProjectId(selectedProjectId);
    } else {
      setSuites([]);
      setSelectedSuiteId("");
    }
  }, [selectedProjectId]);

  const fetchProjects = async () => {
    setIsLoadingProjects(true);
    try {
      const response = await getProjects();
      if (response.items) {
        setProjects(response.items);
      }
    } catch (err) {
      toast({ title: "Error fetching projects", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingProjects(false);
    }
  };

  const fetchSuitesByProjectId = async (projectId: string) => {
    setIsLoadingSuites(true);
    try {
      const response = await getSuitesByProjectId(projectId);
      if (response.items) {
        setSuites(response.items);
      }
    } catch (err) {
      toast({ title: "Error fetching test suites", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingSuites(false);
    }
  };

  const handleEnhanceUserStory = async () => {
    if (!originalUserStory.trim()) {
      toast({ title: "Input Required", description: "Please enter a user story.", variant: "destructive" });
      return;
    }
    setIsLoadingEnhance(true);
    setError(null);
    try {
      const result = await callEnhanceUserStory({
        userStory: originalUserStory,
        language: selectedLanguage
      });
      setEnhancedUserStory(result.enhancedUserStory);
      toast({ title: "User Story Enhanced!" });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Enhancing Story", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingEnhance(false);
    }
  };

  const handleGenerateManualTestCases = async () => {
    if (!finalUserStory.trim()) {
      toast({ title: "User Story Required", description: "A user story is needed to generate test cases.", variant: "destructive" });
      return;
    }
    setIsLoadingManualCases(true);
    setError(null);
    try {
      const result = await callGenerateManualTestCases({
        userStory: finalUserStory,
        language: selectedLanguage
      });
      setGeneratedManualTestCases(result.manualTestCases);
      // Convert test cases to strings for textarea display
      const testCaseStrings = result.manualTestCases.map((tc: string | TestCaseObject) => {
        if (typeof tc === 'string') {
          return tc;
        } else {
          // Handle instrucciones as either string or array
          let stepText = '';
          if (Array.isArray(tc.instrucciones)) {
            stepText = tc.instrucciones.join(' ');
          } else if (typeof tc.instrucciones === 'string') {
            stepText = tc.instrucciones;
          } else {
            stepText = tc.title || '';
          }
          return `${tc.title || tc.id}: ${stepText} Expected: ${tc.expected_results || ''}`;
        }
      });
      setFinalManualTestCases(result.manualTestCases); // Update the test cases array
      toast({ title: "Manual Test Cases Generated!" });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Generating Test Cases", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingManualCases(false);
    }
  };

  const handleGenerateGherkin = async () => {
    if (!finalUserStory.trim() || finalManualTestCases.length === 0 || !targetUrl.trim()) {
      toast({ title: "Inputs Required", description: "User Story, Manual Test Cases, and Target URL are needed.", variant: "destructive" });
      return;
    }
    setIsLoadingGherkin(true);
    setError(null);
    try {
      // Convert test cases to strings for API call
      const testCaseStrings = finalManualTestCases.map((tc: string | TestCaseObject) =>
        typeof tc === 'string'
          ? tc
          : `${tc.title || tc.id}: ${tc.instrucciones || tc.preconditions || ''} Expected: ${tc.expected_results || ''}`
      );
      const instructions = testCaseStrings.join("\n");
      const result = await callGenerateGherkin({
        userStory: finalUserStory,
        instructions,
        url: targetUrl,
        language: selectedLanguage
      });
      setGeneratedGherkin(result.gherkin);
      setGherkinInput(result.gherkin); // Populate textarea
      toast({ title: "Gherkin Scenario Generated!" });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Generating Gherkin", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingGherkin(false);
    }
  };

  const handleExecuteSmokeTest = async () => {
    if (!finalGherkin) {
      toast({ title: "Gherkin Scenario Required", description: "A Gherkin scenario is needed to run a smoke test.", variant: "destructive" });
      return;
    }
    setIsLoadingExecution(true);
    setCurrentExecutionStatus(ExecutionStatus.RUNNING);
    setError(null);
    setTestExecutionResult(null);

    try {
      const request: SmokeTestRequest = {
        type: ExecutionTypeEnum.SMOKE,
        url: targetUrl,
        instructions: finalGherkin,
      };
      const result: ExecutionResponseV2 = await executeTest(request);
      setTestExecutionResult(result);
      setCurrentExecutionId(result.execution_id);
      setCurrentExecutionStatus(result.status as ExecutionStatus);
      toast({ title: "Smoke Test Execution Started", description: `Execution ID: ${result.execution_id}` });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Starting Smoke Test", description: (err as Error).message, variant: "destructive" });
      setCurrentExecutionStatus(ExecutionStatus.ERROR);
    } finally {
      setIsLoadingExecution(false);
    }
  };

  const handleExecuteFullTest = async () => {
    if (!finalGherkin) {
      toast({ title: "Gherkin Scenario Required", description: "A Gherkin scenario is needed to run a full test.", variant: "destructive" });
      return;
    }
    setIsLoadingExecution(true);
    setCurrentExecutionStatus(ExecutionStatus.RUNNING);
    setError(null);
    setTestExecutionResult(null);

    try {
      const request: FullTestRequest = {
        type: ExecutionTypeEnum.FULL,
        url: targetUrl,
        gherkin_scenarios: [finalGherkin],
      };
      const result: ExecutionResponseV2 = await executeTest(request);
      setTestExecutionResult(result);
      setCurrentExecutionId(result.execution_id);
      setCurrentExecutionStatus(result.status as ExecutionStatus);
      toast({ title: "Full Test Execution Started", description: `Execution ID: ${result.execution_id}` });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Starting Full Test", description: (err as Error).message, variant: "destructive" });
      setCurrentExecutionStatus(ExecutionStatus.ERROR);
    } finally {
      setIsLoadingExecution(false);
    }
  };

  const handlePause = async () => {
    if (!currentExecutionId) return;
    try {
      await pauseExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.PAUSED);
      toast({ title: "Execution Paused" });
    } catch (error) {
      toast({ title: "Error Pausing Execution", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleResume = async () => {
    if (!currentExecutionId) return;
    try {
      await resumeExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.RUNNING);
      toast({ title: "Execution Resumed" });
    } catch (error) {
      toast({ title: "Error Resuming Execution", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleStop = async () => {
    if (!currentExecutionId) return;
    try {
      await stopExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.CANCELLED);
      toast({ title: "Execution Cancelled" });
    } catch (error) {
      toast({ title: "Error Cancelling Execution", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleSaveTestCases = async () => {
    if (!selectedProjectId || !selectedSuiteId || !testName.trim() || !testDescription.trim()) {
      toast({ title: "All fields are required", variant: "destructive" });
      return;
    }
    setIsSaving(true);
    setError(null);
    try {
      // Create test cases from the generated manual test cases
      const createdTestCases = [];

      for (let i = 0; i < finalManualTestCases.length; i++) {
        const testCase = finalManualTestCases[i];

        let testCaseName: string;
        let testCaseInstructions: string;

        if (typeof testCase === 'string') {
          testCaseName = `${testName} - Test Case ${i + 1}`;
          testCaseInstructions = testCase;
        } else {
          testCaseName = testCase.title || testCase.id || `${testName} - Test Case ${i + 1}`;
          testCaseInstructions = Array.isArray(testCase.instrucciones)
            ? testCase.instrucciones.join('\n')
            : (testCase.instrucciones || testCase.preconditions || '');
        }

        const testCaseInput = {
          name: testCaseName,
          description: testDescription + (i > 0 ? ` (Test Case ${i + 1})` : ''),
          instrucciones: testCaseInstructions,
          historia_de_usuario: finalUserStory,
          gherkin: finalGherkin || '',
          url: targetUrl,
          tags: ["qa-assistant", "generated"],
        };

        const createdTestCase = await createTestCase(selectedProjectId, selectedSuiteId, testCaseInput);
        createdTestCases.push(createdTestCase);
      }

      toast({
        title: "Test Cases Saved Successfully!",
        description: `${createdTestCases.length} test case(s) have been created in the project.`
      });
      setShowSaveDialog(false);

      // Optionally navigate to the first created test case
      if (createdTestCases.length > 0) {
        // You could navigate to the suite or first test case here if desired
        // router.push(`/projects/${selectedProjectId}/suites/${selectedSuiteId}/tests/${createdTestCases[0].test_id}`);
      }

    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error saving test cases", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveTestHistory = async () => {
    if (!selectedProjectId || !selectedSuiteId || !testName.trim() || !testDescription.trim()) {
      toast({ title: "All fields are required", variant: "destructive" });
      return;
    }
    setIsSaving(true);
    setError(null);

    try {
      // 1. Save test history using the API (only if execution results exist)
      if (testExecutionResult) {
        await saveTestHistory({
          projectId: selectedProjectId,
          suiteId: selectedSuiteId,
          name: testName,
          description: testDescription,
          gherkin: finalGherkin || '',
          testHistory: testExecutionResult
        });
      }

      // 2. Create individual test cases from the manual test cases
      const createdTestCases = [];

      for (let i = 0; i < finalManualTestCases.length; i++) {
        const testCase = finalManualTestCases[i];

        let testCaseName: string;
        let testCaseInstructions: string;
        let testCaseExpected: string = '';
        let testCasePriority: string = 'Medium';

        if (typeof testCase === 'string') {
          testCaseName = `${testName} - Test Case ${i + 1}`;
          testCaseInstructions = testCase;
        } else {
          testCaseName = testCase.title || testCase.id || `${testName} - Test Case ${i + 1}`;
          testCaseInstructions = Array.isArray(testCase.instrucciones)
            ? testCase.instrucciones.join('\n')
            : (testCase.instrucciones || testCase.preconditions || '');
          testCaseExpected = testCase.expected_results || '';
          testCasePriority = testCase.priority || 'Medium';
        }

        const testCaseInput = {
          name: testCaseName,
          description: `${testDescription}\n\nGenerated via QA Assistant\nUser Story: ${finalUserStory}`,
          instrucciones: testCaseInstructions,
          expected_results: testCaseExpected,
          historia_de_usuario: finalUserStory,
          gherkin: finalGherkin || '',
          url: targetUrl,
          priority: testCasePriority,
          tags: ["qa-assistant", "generated", `priority-${testCasePriority.toLowerCase()}`],
          metadata: {
            generated_via: "qa-assistant",
            original_user_story: originalUserStory,
            enhanced_user_story: enhancedUserStory,
            target_url: targetUrl,
            language: selectedLanguage,
            creation_date: new Date().toISOString(),
            has_execution_results: !!testExecutionResult
          }
        };

        const createdTestCase = await createTestCase(selectedProjectId, selectedSuiteId, testCaseInput);
        createdTestCases.push(createdTestCase);
      }

      const resultsMessage = testExecutionResult
        ? `Complete workflow saved: ${createdTestCases.length} test case(s), Gherkin scenario, and execution results have been saved to the project.`
        : `QA workflow saved: ${createdTestCases.length} test case(s) and Gherkin scenario have been saved to the project. You can execute tests later.`;

      toast({
        title: "QA Assistant Results Saved Successfully!",
        description: resultsMessage
      });

      setShowSaveDialog(false);

    } catch (err) {
      setError((err as Error).message);
      toast({
        title: "Error saving QA Assistant results",
        description: (err as Error).message,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const next = () => {
    setError(null);
    if (currentStep === "userStory") {
      if (!originalUserStory.trim() || !targetUrl.trim()) {
        toast({ title: "Input Required", description: "Please provide the original user story and target URL.", variant: "destructive" });
        return;
      }
      setFinalUserStory(enhancedUserStory || originalUserStory);
      setCurrentStep("manualTests");
    } else if (currentStep === "manualTests") {
      // Use the test cases from the table (either generated or manually added)
      if (finalManualTestCases.length === 0) {
        toast({ title: "Input Required", description: "Please provide or generate manual test cases.", variant: "destructive" });
        return;
      }
      setCurrentStep("gherkin");
    } else if (currentStep === "gherkin") {
      if (!gherkinInput.trim()) {
        toast({ title: "Input Required", description: "Please provide or generate a Gherkin scenario.", variant: "destructive" });
        return;
      }
      setFinalGherkin(gherkinInput);
      setCurrentStep("summary");
    }
  };

  const prev = () => {
    setError(null);
    if (currentStep === "summary") setCurrentStep("gherkin");
    else if (currentStep === "gherkin") setCurrentStep("manualTests");
    else if (currentStep === "manualTests") setCurrentStep("userStory");
  };

  const renderStep = () => {
    switch (currentStep) {
      case "userStory":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><Lightbulb className="h-6 w-6 text-primary" />Step 1: User Story & Target URL</CardTitle>
              <CardDescription>Provide the initial user story and the target URL for your test.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="targetUrl">Target URL</Label>
                  <Input id="targetUrl" placeholder="https://example.com" value={targetUrl} onChange={(e) => setTargetUrl(e.target.value)} />
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="originalUserStory">Original User Story</Label>
                <Textarea id="originalUserStory" placeholder="As a user, I want to..." value={originalUserStory} onChange={(e) => setOriginalUserStory(e.target.value)} className="min-h-[100px]" />
              </div>
              <Button onClick={handleEnhanceUserStory} disabled={isLoadingEnhance || !originalUserStory.trim()} variant="outline">
                {isLoadingEnhance ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                Enhance with AI
              </Button>
              {enhancedUserStory && (
                <div className="mt-4 p-4 border rounded-md bg-muted">
                  <div className="flex items-center justify-between">
                    <Label className="font-semibold" htmlFor="editedUserStory">AI Enhanced User Story:</Label>
                    <div className="text-sm text-muted-foreground">
                      {selectedLanguage !== "English" && `Generated in ${selectedLanguage}`}
                    </div>
                  </div>
                  <div className="relative mt-2">
                    <Textarea
                      id="editedUserStory"
                      className="min-h-[150px] text-sm"
                      value={enhancedUserStory}
                      onChange={(e) => setEnhancedUserStory(e.target.value)}
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute top-2 right-2 text-xs opacity-70 hover:opacity-100"
                      onClick={() => setEnhancedUserStory(null)}
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={next} disabled={!originalUserStory.trim() || !targetUrl.trim()}>Next <ChevronRight className="ml-2 h-4 w-4" /></Button>
            </CardFooter>
          </Card>
        );
      case "manualTests":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><ListChecks className="h-6 w-6 text-primary" />Step 2: Manual Test Cases</CardTitle>
              <CardDescription>Generate or manually input your test cases based on the user story.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-3 border rounded-md bg-muted/50">
                <Label className="font-semibold">Selected User Story:</Label>
                <p className="text-sm whitespace-pre-wrap">{finalUserStory}</p>
              </div>
              <div className="p-3 border rounded-md bg-muted/50">
                <Label className="font-semibold">Target URL:</Label>
                <p className="text-sm whitespace-pre-wrap">{targetUrl}</p>
              </div>
              <Button onClick={handleGenerateManualTestCases} disabled={isLoadingManualCases} variant="outline">
                {isLoadingManualCases ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                Generate with AI
              </Button>
              <div>
                <Label htmlFor="manualTestCasesInput">Manual Test Cases</Label>
                <div className="mt-2">
                  <ManualTestCasesTable
                    testCases={finalManualTestCases || []}
                    onTestCasesChange={(updatedRows) => {
                      // Convert table rows back to test case objects for finalManualTestCases
                      const convertedTestCases = updatedRows.map(row => ({
                        id: row.caseId,
                        title: row.step.split(':')[0] || row.caseId,
                        preconditions: "",
                        instrucciones: row.step,
                        expected_results: row.expected,
                        priority: row.priority || "Medium",
                        historia_de_usuario: row.userStory || finalUserStory
                      }));
                      setFinalManualTestCases(convertedTestCases);
                    }}
                  />
                  {generatedManualTestCases && generatedManualTestCases.length > 0 && (
                    <div className="mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setGeneratedManualTestCases([]);
                          setFinalManualTestCases([]);
                        }}
                      >
                        Clear Generated Tests
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={prev} variant="outline"><ChevronLeft className="mr-2 h-4 w-4" /> Previous</Button>
              <Button onClick={() => {
                // finalManualTestCases is already up-to-date from the table component
                next();
              }}>Next <ChevronRight className="ml-2 h-4 w-4" /></Button>
            </CardFooter>
          </Card>
        );
      case "gherkin":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><FileText className="h-6 w-6 text-primary" />Step 3: Gherkin Scenario</CardTitle>
              <CardDescription>Generate or manually write the Gherkin scenario.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-3 border rounded-md bg-muted/50 max-h-28 overflow-y-auto">
                <Label className="font-semibold">Selected User Story:</Label>
                <p className="text-sm whitespace-pre-wrap">{finalUserStory}</p>
              </div>
              <div className="p-3 border rounded-md bg-muted/50 max-h-28 overflow-y-auto">
                <Label className="font-semibold">Manual Test Cases:</Label>
                <ul className="list-disc list-inside text-sm">
                  {finalManualTestCases.map((tc, i) => (
                    <li key={i}>
                      {typeof tc === 'string'
                        ? tc
                        : `${tc.title || tc.id}: ${tc.instrucciones || tc.preconditions || ''}`
                      }
                    </li>
                  ))}
                </ul>
              </div>
              <Button onClick={handleGenerateGherkin} disabled={isLoadingGherkin} variant="outline">
                {isLoadingGherkin ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                Generate with AI
              </Button>
              <div>
                <Label htmlFor="gherkinInput">Gherkin Scenario</Label>
                <div className="mt-2">
                  <GherkinEditor
                    value={gherkinInput}
                    onChange={(value) => setGherkinInput(value)}
                    placeholder="Feature: User Login..."
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={prev} variant="outline"><ChevronLeft className="mr-2 h-4 w-4" /> Previous</Button>
              <Button onClick={next} disabled={!gherkinInput.trim()}>Finish & View Summary <ChevronRight className="ml-2 h-4 w-4" /></Button>
            </CardFooter>
          </Card>
        );
      case "summary":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">QA Assistant Summary</CardTitle>
              <CardDescription>Here are the artifacts from your session.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Card className="shadow-none">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Final User Story</CardTitle>
                    {selectedLanguage !== "English" && (
                      <Badge variant="outline">{selectedLanguage}</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent><ScrollArea className="h-24 w-full rounded-md border p-3 bg-muted/30"><pre className="text-sm whitespace-pre-wrap">{finalUserStory}</pre></ScrollArea></CardContent>
              </Card>
              <Card className="shadow-none">
                <CardHeader className="pb-2"><CardTitle className="text-lg">Target URL</CardTitle></CardHeader>
                <CardContent><p className="text-sm p-3 border rounded-md bg-muted/30">{targetUrl}</p></CardContent>
              </Card>
              <Card className="shadow-none">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Manual Test Cases</CardTitle>
                    {selectedLanguage !== "English" && (
                      <Badge variant="outline">{selectedLanguage}</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md">
                    <ManualTestCasesTable testCases={finalManualTestCases} />
                  </div>
                </CardContent>
              </Card>
              <Card className="shadow-none">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Gherkin Scenario</CardTitle>
                    {selectedLanguage !== "English" && (
                      <Badge variant="outline">{selectedLanguage}</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {finalGherkin && (
                    <GherkinEditor
                      value={finalGherkin}
                      onChange={() => { }} // Read-only mode
                      readOnly={true}
                      className="border-0" // Remove extra border since it's inside a Card
                    />
                  )}
                </CardContent>
              </Card>
              <div className="flex items-center gap-4 mt-4">
                <Button onClick={handleExecuteSmokeTest} disabled={isLoadingExecution || !finalGherkin}>
                  <Flame className="w-4 h-4 mr-2" />
                  Execute Smoke Test
                </Button>
                <Button onClick={handleExecuteFullTest} disabled={isLoadingExecution || !finalGherkin}>
                  <Play className="w-4 h-4 mr-2" />
                  Execute Full Test
                </Button>
                {isLoadingExecution || (currentExecutionStatus === ExecutionStatus.RUNNING || currentExecutionStatus === ExecutionStatus.PAUSED) ? (
                  <div className="flex gap-2">
                    {currentExecutionStatus === ExecutionStatus.RUNNING && (
                      <Button onClick={handlePause} variant="outline">Pause</Button>
                    )}
                    {currentExecutionStatus === ExecutionStatus.PAUSED && (
                      <Button onClick={handleResume} variant="outline">Resume</Button>
                    )}
                    <Button onClick={handleStop} variant="destructive">Cancel</Button>
                  </div>
                ) : null}
              </div>
              {testExecutionResult && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Test Execution Complete</AlertTitle>
                  <AlertDescription>
                    <span className={testExecutionResult.metadata.success ? "text-green-500 font-medium" : "text-red-500 font-medium"}>
                      {testExecutionResult.metadata.success ? "✓ Success" : "✗ Failed"}
                    </span> - Total steps: {testExecutionResult.metadata.total_steps}
                    {testExecutionResult.errors.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium">Errors:</p>
                        <div className="space-y-2">
                          {testExecutionResult.errors.map((error: string, i: number) => (
                            <div key={i} className="text-red-500 text-sm">
                              {error.length > 200 ? (
                                <details className="cursor-pointer">
                                  <summary className="hover:text-red-600">
                                    {error.substring(0, 200)}... (click to expand)
                                  </summary>
                                  <div className="mt-2 pl-4 border-l-2 border-red-200 whitespace-pre-wrap break-words">
                                    {error}
                                  </div>
                                </details>
                              ) : (
                                <span className="whitespace-pre-wrap break-words">{error}</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    <div className="mt-2">
                      <Link href="/smoke-test-playground" className="text-blue-500 hover:underline">
                        View full results in Smoke Test Playground
                      </Link>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
              <Alert>
                <Lightbulb className="h-4 w-4" />
                <AlertTitle>Next Steps</AlertTitle>
                <AlertDescription className="flex flex-col gap-2">
                  <p>You can now save these test results to a project.</p>
                  <Button
                    onClick={() => setShowSaveDialog(true)}
                    variant="outline"
                    className="w-full sm:w-auto"
                    disabled={!finalUserStory.trim() || finalManualTestCases.length === 0}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Test Results to Project
                  </Button>
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={prev} variant="outline"><ChevronLeft className="mr-2 h-4 w-4" /> Back to Gherkin</Button>
              <Button
                onClick={() => setShowSaveDialog(true)}
                className="ml-auto"
                disabled={!finalUserStory.trim() || finalManualTestCases.length === 0}
              >
                <Save className="mr-2 h-4 w-4" />
                Save QA Results to Project
              </Button>
            </CardFooter>
          </Card>
        )
      default:
        return null;
    }
  };

  // Progress indicator
  const steps: { id: Step, name: string }[] = [
    { id: "userStory", name: "User Story" },
    { id: "manualTests", name: "Manual Tests" },
    { id: "gherkin", name: "Gherkin" },
    { id: "summary", name: "Summary" },
  ];
  const currentStepIndex = steps.findIndex(s => s.id === currentStep);


  return (
    <div>
      <h1 className="page-header flex items-center gap-2"><Bot /> Guided QA Assistant</h1>

      {/* Progress Bar Simple */}
      <div className="mb-8 flex justify-center space-x-2 sm:space-x-4">
        {steps.map((step, index) => (
          <div key={step.id} className="flex flex-col items-center">
            <div className="flex items-center">
              {index > 0 && (
                <div className={cn("h-[2px] w-12 bg-muted", index <= currentStepIndex ? "bg-primary" : "")}></div>
              )}
              <div className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border text-foreground",
                index < currentStepIndex ? "border-primary bg-primary text-primary-foreground" :
                  index === currentStepIndex ? "border-primary" : "border-muted"
              )}>
                {index < currentStepIndex ? <Check className="h-4 w-4" /> : index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={cn("h-[2px] w-12 bg-muted", index < currentStepIndex ? "bg-primary" : "")}></div>
              )}
            </div>
            <div className={cn("mt-2 text-sm", index === currentStepIndex ? "font-medium text-foreground" : "text-muted-foreground")}>
              {step.name}
            </div>
          </div>
        ))}
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>An Error Occurred</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {renderStep()}

      {/* Save Test History Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Save QA Assistant Results to Project</DialogTitle>
            <DialogDescription>
              Save the complete QA workflow including user story, manual test cases, Gherkin scenario, and execution results to a project. All test cases will be created individually and can be managed separately.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* Information about what will be saved */}
            <div className="p-4 border rounded-lg bg-muted/50">
              <h4 className="font-semibold mb-2">What will be saved:</h4>
              <ul className="text-sm space-y-1">
                <li>✓ User Story: {enhancedUserStory ? "Enhanced version" : "Original version"}</li>
                <li>✓ Manual Test Cases: {finalManualTestCases.length} test case(s)</li>
                <li>✓ Gherkin Scenario: {finalGherkin ? "Available" : "Not generated yet"}</li>
                <li>{testExecutionResult ? '✓' : '○'} Execution Results: {testExecutionResult ? "Available" : "Not executed yet"}</li>
                <li>✓ Target URL: {targetUrl}</li>
                <li>✓ Language: {selectedLanguage}</li>
              </ul>
            </div>

            <div>
              <Label htmlFor="projectSelect">Project</Label>
              <Select onValueChange={(value) => setSelectedProjectId(value)} disabled={isLoadingProjects}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.project_id} value={project.project_id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="suiteSelect">Test Suite</Label>
              <Select onValueChange={(value) => setSelectedSuiteId(value)} disabled={isLoadingSuites || suites.length === 0}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a test suite" />
                </SelectTrigger>
                <SelectContent>
                  {suites.map((suite) => (
                    <SelectItem key={suite.suite_id} value={suite.suite_id}>
                      {suite.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="testName">Test Name</Label>
              <Input id="testName" value={testName} onChange={(e) => setTestName(e.target.value)} placeholder="Enter a name for the test" />
            </div>
            <div>
              <Label htmlFor="testDescription">Test Description</Label>
              <Textarea id="testDescription" value={testDescription} onChange={(e) => setTestDescription(e.target.value)} placeholder="Enter a description for the test" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTestHistory} disabled={isSaving}>
              {isSaving ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Save QA Results
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  )
}
