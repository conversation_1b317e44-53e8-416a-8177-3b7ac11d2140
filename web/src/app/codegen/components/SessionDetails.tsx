"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Download, 
  RefreshCw, 
  Code, 
  FileText, 
  Clock, 
  Calendar,
  Monitor,
  Copy,
  Check,
  ExternalLink,
  Play,
  ArrowRight,
  BarChart3,
  CheckCircle2,
  XCircle,
  Info,
  Camera,
  Eye,
  AlertTriangle
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ExecutionStepCard } from '@/components/codegen/ExecutionStepCard';
import { UnifiedResultsViewer } from '@/components/execution/UnifiedResultsViewer';
import { VncViewer } from '@/components/codegen/VncViewer';

import {
  getCodegenSession,
  getCodegenGeneratedCode,
  convertCodegenToTestcase,
  executeCodegenTest,
  getCodegenExecution,
  stopCodegenExecution
} from '@/lib/api';

import type { 
  CodegenSessionInfo, 
  CodegenTestCaseRequest,
  CodegenExecutionInfo 
} from '@/lib/types';

interface SessionDetailsProps {
  sessionId: string;
  onRefresh: () => void;
}

export function SessionDetails({ sessionId, onRefresh }: SessionDetailsProps) {
  const [copiedCode, setCopiedCode] = useState(false);
  const [showConvertDialog, setShowConvertDialog] = useState(false);
  const [showExecuteDialog, setShowExecuteDialog] = useState(false);
  const [activeExecution, setActiveExecution] = useState<string | null>(null);
  const [convertFormData, setConvertFormData] = useState({
    test_name: '',
    test_description: '',
    project_id: '',
    test_suite: '',
    user_story: '',
    framework: 'playwright'
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: session, isLoading } = useQuery({
    queryKey: ['codegen-session', sessionId],
    queryFn: () => getCodegenSession(sessionId),
    refetchInterval: 5000,
    enabled: !!sessionId,
  });

  // Set the active execution from the session's last execution
  useEffect(() => {
    if (session?.last_execution?.execution_id && !activeExecution) {
      setActiveExecution(session.last_execution.execution_id);
    }
  }, [session?.last_execution?.execution_id, activeExecution]);


  const { data: codeData, isLoading: codeLoading } = useQuery({
    queryKey: ['codegen-code', sessionId],
    queryFn: () => getCodegenGeneratedCode(sessionId),
    enabled: !!sessionId && (session?.status === 'completed' || session?.status === 'stopped'),
  });

  // Execution query - only when there's an active execution
  const { data: executionData } = useQuery({
    queryKey: ['codegen-execution', activeExecution],
    queryFn: () => getCodegenExecution(activeExecution!),
    enabled: !!activeExecution,
    refetchInterval: 2000, // Refetch more frequently for execution status
  });

  const convertMutation = useMutation({
    mutationFn: convertCodegenToTestcase,
    onSuccess: (data) => {
      toast({
        title: "Test Case Created",
        description: `Successfully converted session to test case: ${data.test_name}`,
      });
      setShowConvertDialog(false);
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
    onError: (error) => {
      toast({
        title: "Conversion Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const executeMutation = useMutation({
    mutationFn: executeCodegenTest,
    onSuccess: (data) => {
      toast({
        title: "Test Execution Started",
        description: "Your test is now running with aery-browser",
      });
      setActiveExecution(data.execution_id);
      queryClient.invalidateQueries({ queryKey: ['codegen-session', sessionId] });
      setShowExecuteDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Execution Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const stopExecutionMutation = useMutation({
    mutationFn: stopCodegenExecution,
    onSuccess: () => {
      toast({
        title: "Execution Stopped",
        description: "Test execution has been stopped",
      });
      queryClient.invalidateQueries({ queryKey: ['codegen-execution', activeExecution] });
    },
    onError: (error) => {
      toast({
        title: "Stop Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCopyCode = async () => {
    if (codeData?.generated_code) {
      await navigator.clipboard.writeText(codeData.generated_code);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
      toast({
        title: "Code Copied",
        description: "Generated code copied to clipboard",
      });
    }
  };

  const handleConvert = () => {
    const request: CodegenTestCaseRequest = {
      session_id: sessionId,
      test_name: convertFormData.test_name,
      test_description: convertFormData.test_description,
      project_id: convertFormData.project_id,
      test_suite: convertFormData.test_suite || undefined,
      user_story: convertFormData.user_story || undefined,
      framework: convertFormData.framework,
      include_assertions: true,
      add_error_handling: true,
    };
    convertMutation.mutate(request);
  };

  const handleExecute = () => {
    executeMutation.mutate({ session_id: sessionId });
  };

  const handleStopExecution = () => {
    if (activeExecution) {
      stopExecutionMutation.mutate(activeExecution);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const getStatusColor = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'starting':
        return 'secondary';
      case 'running':
        return 'default';
      case 'completed':
        return 'default';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'outline';
      case 'completed_with_failures':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!session) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64 text-muted-foreground">
          Session not found
        </CardContent>
      </Card>
    );
  }

  return (
    <div>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center">
                <Monitor className="mr-2" />
                Codegen Session
              </CardTitle>
              <CardDescription>Session ID: {session.session_id}</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button onClick={() => onRefresh()} variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button onClick={() => setShowExecuteDialog(true)} disabled={session.status === 'running'}>
                <Play className="mr-2 h-4 w-4" />
                Execute Test
              </Button>
              <Button onClick={() => setShowConvertDialog(true)} disabled={!codeData?.generated_code}>
                <ArrowRight className="mr-2 h-4 w-4" />
                Convert to Test Case
              </Button>
            </div>
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground pt-4">
            <div className="flex items-center">
              <Badge variant={getStatusColor(session.status)}>{session.status}</Badge>
            </div>
            <div className="flex items-center">
              <Calendar className="mr-1 h-4 w-4" />
              Created on {formatDateTime(session.created_at)}
            </div>
            <div className="flex items-center">
              <Clock className="mr-1 h-4 w-4" />
              Last updated {formatDateTime(session.updated_at)}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue={activeExecution ? "execution" : (session.vnc_info ? "vnc" : "code")}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="execution" disabled={!activeExecution}>
                <Play className="mr-2 h-4 w-4" />
                Execution
              </TabsTrigger>
              <TabsTrigger value="vnc" disabled={!session.vnc_info}>
                <Monitor className="mr-2 h-4 w-4" />
                Remote Browser
              </TabsTrigger>
              <TabsTrigger value="code">
                <Code className="mr-2 h-4 w-4" />
                Generated Code
              </TabsTrigger>
              <TabsTrigger value="screenshots" disabled={!executionData?.screenshots?.length}>
                <Camera className="mr-2 h-4 w-4" />
                Screenshots
              </TabsTrigger>
              <TabsTrigger value="details">
                <Info className="mr-2 h-4 w-4" />
                Details
              </TabsTrigger>
            </TabsList>
            <TabsContent value="execution">
              {executionData ? (
                <div className="space-y-4 pt-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold">Execution Status</h3>
                    <Badge variant={getStatusColor(executionData.status)}>
                      {executionData.status}
                    </Badge>
                  </div>
                  
                  {/* Use unified results viewer for StandardResult format */}
                  <UnifiedResultsViewer data={executionData} />
                  {(executionData.status === 'running' || executionData.status === 'starting') && (
                    <Button onClick={handleStopExecution} variant="destructive" disabled={stopExecutionMutation.isPending}>
                      Stop Execution
                    </Button>
                  )}
                </div>
              ) : (
                <div className="text-center text-muted-foreground p-8">
                  No active execution to display.
                </div>
              )}
            </TabsContent>
            <TabsContent value="vnc">
              {session.vnc_info ? (
                <VncViewer 
                  vncInfo={session.vnc_info} 
                  className="mt-4"
                />
              ) : (
                <div className="text-center text-muted-foreground p-8">
                  <Monitor className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Remote browser access not available for this session.</p>
                  <p className="text-sm mt-2">
                    VNC access is only available for headless environments or when forced VNC mode is enabled.
                  </p>
                </div>
              )}
            </TabsContent>
            <TabsContent value="code">
              <Card>
                <CardHeader>
                  <CardTitle>Generated Code</CardTitle>
                  <CardDescription>
                    This is the Playwright code generated from your interactions.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {codeLoading ? (
                    <Skeleton className="h-64 w-full" />
                  ) : codeData?.generated_code ? (
                    <div className="relative">
                      <ScrollArea className="h-64 font-mono text-sm border rounded-md p-4">
                        <pre><code>{codeData.generated_code}</code></pre>
                      </ScrollArea>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2"
                        onClick={handleCopyCode}
                      >
                        {copiedCode ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground p-8">
                      No code generated yet. Session might be running or stopped early.
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
             <TabsContent value="screenshots">
              <Card>
                <CardHeader>
                  <CardTitle>Screenshots</CardTitle>
                  <CardDescription>
                    Screenshots taken during the execution.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {executionData?.screenshots && executionData.screenshots.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {executionData.screenshots.map((screenshot, index) => (
                        <Dialog key={index}>
                          <DialogTrigger asChild>
                             <img 
                              src={screenshot} 
                              alt={`Screenshot ${index + 1}`} 
                              className="w-full h-auto object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                            />
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl">
                             <img 
                              src={screenshot} 
                              alt={`Screenshot ${index + 1}`} 
                              className="w-full h-auto rounded-lg"
                            />
                          </DialogContent>
                        </Dialog>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No screenshots available.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Session Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Session Info</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <h4 className="font-semibold w-32">URL</h4>
                        <a href={session.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline flex items-center">
                          {session.url}
                          <ExternalLink className="ml-2 h-4 w-4" />
                        </a>
                      </div>
                      <div className="flex items-center">
                        <h4 className="font-semibold w-32">Language</h4>
                        <span className="text-muted-foreground">{session.target_language}</span>
                      </div>
                    </div>
                  </div>

                  {/* VNC Remote Access Section */}
                  {session.vnc_info && (
                    <div>
                      <h4 className="font-semibold mb-2 mt-4 flex items-center">
                        <Monitor className="mr-2 h-4 w-4" />
                        Remote Access (VNC)
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Web VNC</h4>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => window.open(session.web_vnc_url, '_blank')}
                            className="flex items-center"
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Open Browser VNC
                          </Button>
                        </div>
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">VNC Port</h4>
                          <span className="text-muted-foreground">{session.vnc_port}</span>
                        </div>
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Display</h4>
                          <span className="text-muted-foreground">:{session.vnc_info.display_number}</span>
                        </div>
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Status</h4>
                          <Badge variant={session.vnc_info.status === 'running' ? 'default' : 'secondary'}>
                            {session.vnc_info.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  )}

                  {executionData && (
                    <div>
                      <h4 className="font-semibold mb-2 mt-4">Latest Execution</h4>
                       <div className="space-y-2">
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Execution ID</h4>
                          <span className="text-muted-foreground">{executionData.execution_id}</span>
                        </div>
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Status</h4>
                          <Badge variant={getStatusColor(executionData.status)}>{executionData.status}</Badge>
                        </div>
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Created At</h4>
                          <span className="text-muted-foreground">{formatDateTime(executionData.created_at)}</span>
                        </div>
                        <div className="flex items-center">
                          <h4 className="font-semibold w-32">Updated At</h4>
                          <span className="text-muted-foreground">{formatDateTime(executionData.updated_at)}</span>
                        </div>
                        {executionData.completed_at && (
                          <div className="flex items-center">
                            <h4 className="font-semibold w-32">Completed At</h4>
                            <span className="text-muted-foreground">{formatDateTime(executionData.completed_at)}</span>
                          </div>
                        )}
                        {executionData.final_result && (
                          <div className="flex items-start">
                            <h4 className="font-semibold w-32 shrink-0">Final Result</h4>
                            <p className="text-muted-foreground bg-gray-100 dark:bg-gray-800 p-2 rounded-md text-sm">
                              {executionData.final_result}
                            </p>
                          </div>
                        )}
                        {executionData.error && (
                          <div className="flex items-start">
                            <h4 className="font-semibold w-32 shrink-0">Error</h4>
                            <p className="text-red-500 bg-red-100 dark:bg-red-900/50 p-2 rounded-md text-sm">
                              {executionData.error}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog open={showConvertDialog} onOpenChange={setShowConvertDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Convert to Test Case</DialogTitle>
            <DialogDescription>
              This will create a new test case from the generated code.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="test_name">Test Name</Label>
              <Input
                id="test_name"
                value={convertFormData.test_name}
                onChange={(e) => setConvertFormData({ ...convertFormData, test_name: e.target.value })}
                placeholder="e.g., Verify user login"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="test_description">Test Description</Label>
              <Textarea
                id="test_description"
                value={convertFormData.test_description}
                onChange={(e) => setConvertFormData({ ...convertFormData, test_description: e.target.value })}
                placeholder="A short description of what this test case does."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConvertDialog(false)}>Cancel</Button>
            <Button onClick={handleConvert} disabled={convertMutation.isPending}>
              {convertMutation.isPending ? 'Converting...' : 'Convert'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <Dialog open={showExecuteDialog} onOpenChange={setShowExecuteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Execute Test</DialogTitle>
            <DialogDescription>
              Are you sure you want to run this test? This will start a new aery-browser session.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExecuteDialog(false)}>Cancel</Button>
            <Button onClick={handleExecute} disabled={executeMutation.isPending}>
              {executeMutation.isPending ? 'Starting...' : 'Execute'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
