#!/usr/bin/env python3
"""
Script de migración para agregar el campo execution_types a configuraciones existentes.

Este script actualiza todas las configuraciones de navegador existentes en MongoDB
para incluir el nuevo campo execution_types con valores por defecto.
"""

import asyncio
import logging
from datetime import datetime
from typing import List

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def migrate_execution_types():
    """
    Migra configuraciones existentes para agregar el campo execution_types.
    """
    try:
        # Importar después de configurar el entorno
        from src.database.models.browser_configuration import BrowserConfiguration, ExecutionType
        from src.database.connection import init_database
        
        # Inicializar conexión a la base de datos
        await init_database()
        
        logger.info("Iniciando migración de execution_types...")
        
        # Obtener todas las configuraciones
        configurations = await BrowserConfiguration.find_all().to_list()
        
        if not configurations:
            logger.info("No se encontraron configuraciones para migrar.")
            return
        
        logger.info(f"Encontradas {len(configurations)} configuraciones para migrar.")
        
        migrated_count = 0
        skipped_count = 0
        
        for config in configurations:
            try:
                # Verificar si ya tiene el campo execution_types
                if hasattr(config, 'execution_types') and config.execution_types:
                    logger.debug(f"Configuración {config.config_id} ya tiene execution_types, omitiendo.")
                    skipped_count += 1
                    continue
                
                # Agregar execution_types por defecto (todos los tipos)
                config.execution_types = [
                    ExecutionType.SMOKE,
                    ExecutionType.FULL,
                    ExecutionType.CASE,
                    ExecutionType.SUITE,
                    ExecutionType.CODEGEN
                ]
                
                # Actualizar timestamp
                config.updated_at = datetime.utcnow()
                
                # Guardar configuración
                await config.save()
                
                logger.info(f"Migrada configuración: {config.name} ({config.config_id})")
                migrated_count += 1
                
            except Exception as e:
                logger.error(f"Error migrando configuración {config.config_id}: {str(e)}")
                continue
        
        logger.info(f"Migración completada:")
        logger.info(f"  - Configuraciones migradas: {migrated_count}")
        logger.info(f"  - Configuraciones omitidas: {skipped_count}")
        logger.info(f"  - Total procesadas: {len(configurations)}")
        
    except Exception as e:
        logger.error(f"Error durante la migración: {str(e)}")
        raise


async def verify_migration():
    """
    Verifica que la migración se haya completado correctamente.
    """
    try:
        from src.database.models.browser_configuration import BrowserConfiguration
        
        logger.info("Verificando migración...")
        
        # Obtener todas las configuraciones
        configurations = await BrowserConfiguration.find_all().to_list()
        
        missing_execution_types = 0
        valid_configurations = 0
        
        for config in configurations:
            if not hasattr(config, 'execution_types') or not config.execution_types:
                logger.warning(f"Configuración {config.config_id} no tiene execution_types")
                missing_execution_types += 1
            else:
                valid_configurations += 1
        
        logger.info(f"Verificación completada:")
        logger.info(f"  - Configuraciones válidas: {valid_configurations}")
        logger.info(f"  - Configuraciones sin execution_types: {missing_execution_types}")
        
        if missing_execution_types == 0:
            logger.info("✅ Migración verificada exitosamente")
        else:
            logger.warning(f"⚠️  {missing_execution_types} configuraciones necesitan migración")
        
    except Exception as e:
        logger.error(f"Error durante la verificación: {str(e)}")
        raise


async def main():
    """
    Función principal del script de migración.
    """
    try:
        logger.info("=== Iniciando script de migración de execution_types ===")
        
        # Ejecutar migración
        await migrate_execution_types()
        
        # Verificar migración
        await verify_migration()
        
        logger.info("=== Script de migración completado ===")
        
    except Exception as e:
        logger.error(f"Error en el script de migración: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    import os
    
    # Agregar el directorio raíz al path para importar módulos
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Ejecutar migración
    exit_code = asyncio.run(main())
    sys.exit(exit_code)