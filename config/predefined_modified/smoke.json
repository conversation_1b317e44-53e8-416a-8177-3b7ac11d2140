{"config_type": "smoke", "name": "Smoke", "description": "Configuración para pruebas rápidas de funcionalidad básica - smoke testing", "settings": {"model_name": "openai/gpt-4.1-mini", "embedder_model": "all-MiniLM-L6-v2", "embedder_provider": "huggingface", "embedder_dims": 384, "model_provider": "openrouter", "temperature": 0.1, "memory_agent_id": "browser_use_agent"}, "execution_types": ["smoke"], "updated_at": "2025-07-09T21:56:40.573963", "is_modified": true, "warnings": []}