[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "qak"
version = "1.0.0"
description = "QAK Test Execution System"
requires-python = ">=3.11"
dependencies = []

# Configuración de black para formateo de código
[tool.black]
line-length = 120
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | node_modules
    | static
    | web
  )/
)
'''

# Configuración de isort para ordenamiento de imports
[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["src", "libs"]
skip_glob = [
    "**/node_modules/**",
    "**/static/**",
    "**/web/**",
    "**/.venv/**",
    "**/venv/**"
]
known_first_party = ["src", "libs", "browser_use"]
known_third_party = [
    "fastapi",
    "pydantic",
    "uvicorn",
    "celery",
    "redis",
    "pymongo",
    "motor",
    "playwright",
    "anthropic",
    "openai",
    "google"
]

# Configuración de coverage para cobertura de tests
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "*/static/*",
    "*/web/*",
    "*/node_modules/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*Protocol",
    "@abstractmethod"
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Configuración de pytest
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
    "src/tests"
]
python_files = [
    "test_*.py",
    "*_test.py"
]
python_classes = [
    "Test*"
]
python_functions = [
    "test_*"
]
markers = [
    "slow: marks tests as slow (deselect with '-m 'not slow'')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "browser: marks tests that require browser automation"
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning"
]