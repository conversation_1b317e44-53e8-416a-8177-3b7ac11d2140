# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

**Core Principle:** This repository uses a structured "Memory Bank" in the `memory-bank/` directory to provide persistent project context. Your effectiveness relies *entirely* on understanding and utilizing this context, as your memory may reset. **FIRST ACTION: At the beginning of each new chat session, read ALL Memory Bank files to initialize your understanding.** **Never proceed without complete context from these files.**

## Memory Bank Files & Purpose:

Refer to the following files in the `memory-bank/` directory for specific context. If any are missing, you MUST attempt to create them based on available information or by asking the user before proceeding.

*   **`project-brief.md`**: Overall project goals, scope, and success criteria. (High-level understanding).
*   **`productContext.md`**: The "why" behind the project, problems it solves, how it should work. (User needs, product features).
*   **`techContext.md`**: Technologies used, development setup, technical constraints. (Implementation details, dependencies, limitations).
*   **`systemPatterns.md`**: System architecture, key technical decisions, design patterns, component relationships. (System structure and organization).
*   **`activeContext.md`**: Current work focus, recent changes, next steps, active decisions. (**Your primary source of truth for current state**).
*   **`progress.md`**: What works, what's left to build, current status, known issues. (Project status assessment).

## Project Overview

QAK (Quality Assurance Kit) is an AI-powered test automation platform that transforms user stories into executable automation code using browser automation and LLM integration.

## Architecture

### Core Components

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS, Radix UI (port 9001)
- **Backend**: FastAPI with Python 3.11+ (port 8000)
- **Database**: MongoDB with Beanie ODM (async)
- **Browser Automation**: Custom browser-use library with Playwright
- **LLM Integration**: OpenRouter-based multi-provider architecture

### Key Directories

- `app.py` - Main FastAPI application entry point
- `cli.py` - Command-line interface for smoke tests
- `src/` - Main source code
  - `api/` - REST API endpoints and routes
  - `core/` - Core business logic and services
  - `database/` - MongoDB models and repositories
  - `services/` - Service layer (including modern LLM architecture)
  - `utilities/` - Helper utilities
- `web/` - Next.js frontend application
- `libs/browser_use/` - Custom browser automation library
- `memory-bank/` - Project context and AI documentation
- `prompts/` - Versioned markdown-based prompt templates

## Common Development Commands

### Backend Development
```bash
# Setup virtual environment (recommended)
python3.11 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers (required for browser-use)
playwright install

# Start API server
python app.py
```

### Frontend Development
```bash
# Start Next.js dev server (run from web/ directory)
cd web && npm run dev

# Build frontend
cd web && npm run build

# Type checking
cd web && npm run typecheck
```

### Docker Development
```bash
# Development mode with hot reload
docker-compose -f docker-compose.dev.yml up --build -d

# Production mode
docker-compose up --build -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop containers
docker-compose -f docker-compose.dev.yml down
```

## LLM Architecture (Modern)

The codebase uses a modern LLM architecture centered around OpenRouter:

### Key Services
- `src/services/llm/` - Modern LLM service architecture
- `src/services/llm/llm_service_factory.py` - Factory for LLM provider selection
- `src/services/llm/providers/openrouter_service.py` - Primary LLM provider
- `src/services/llm/use_cases/` - Specific LLM use cases (Gherkin, translation, etc.)

### Configuration
- Primary: OpenRouter with automatic fallback to requests-based implementation
- Fallback: Direct Gemini integration
- Prompts: Versioned markdown files in `prompts/` directory

## Test Execution Architecture

### V2 Modern Architecture (Current)
- **Endpoint**: `/api/v2/tests/execute` - Unified execution endpoint
- **Orchestrator**: `src/core/execution_orchestrator.py` - Centralized async coordination
- **Strategies**: `src/core/execution_strategies.py` - Pattern-based execution
  - `SmokeTestStrategy` - Direct instruction execution
  - `FullTestStrategy` - Complete pipeline from user story to code
  - `TestCaseStrategy` - Individual test case execution
  - `SuiteStrategy` - Batch execution with parallel support

### Execution Types
- **Smoke Test**: Direct execution from natural language instructions
- **Full Test**: Complete pipeline (user story → manual tests → Gherkin → execution → code generation)
- **Suite Execution**: Batch execution of multiple test cases
- **Code Generation**: Multi-framework automation code generation

## Database Models

### Key Models (MongoDB with Beanie ODM)
- `Project` - Container for test organization
- `TestSuite` - Collection of related test cases
- `TestCase` - Individual test with Gherkin scenarios or instructions
- `Execution` - Test execution records with results and artifacts
- `CodegenSession` - Code generation session management

### Database Connection
- Async MongoDB connection via Motor driver
- Models in `src/database/models/`
- Repositories in `src/database/repositories/`

## Browser Automation

### Custom Browser-Use Library
- Location: `libs/browser_use/`
- Based on modified browser-use with AI-powered browser interaction
- EventBus system for agent communication
- Memory system for contextual browser sessions
- Automatic screenshot capture and artifact generation

### Key Features
- Real browser automation with Chrome/Chromium
- AI-powered element interaction
- Pattern learning and recognition
- Contextual memory between sessions

## Code Generation

### Supported Frameworks
- Selenium + PyTest BDD (Python)
- Playwright (Python)
- Cypress (JavaScript)
- Robot Framework
- Selenium + Cucumber (Java)

### Generation Process
1. Execute test in browser with AI agent
2. Capture interaction patterns and screenshots
3. Generate framework-specific code using LLM
4. Store in persistent `codegen_sessions/artifacts/` directory

## Environment Configuration

### Required Environment Variables
```bash
# First, ensure virtual environment is activated
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Then set environment variables
GOOGLE_API_KEY=your_google_api_key
OPENROUTER_API_KEY=your_openrouter_key  # Optional, falls back to Gemini
LLM_MODEL=gemini-2.5-flash-exp  # Default model
PROMPT_LANGUAGE=es  # Prompt language (es/en)
```

### Optional Configuration
- `OPENAI_API_KEY` - For OpenAI provider
- `ANTHROPIC_API_KEY` - For Anthropic provider
- `REDIS_URL` - For caching (optional)
- `MONGODB_URL` - MongoDB connection (defaults to local)

## Memory Bank System

The project uses a structured Memory Bank system in `memory-bank/` for persistent AI context:

- `activeContext.md` - Current work focus and recent changes
- `project-brief.md` - Project goals and scope
- `productContext.md` - Product features and user needs
- `techContext.md` - Technical implementation details
- `systemPatterns.md` - Architecture and design patterns
- `progress.md` - Current status and known issues

## Recent Major Changes

### LLM Architecture Modernization (2025-07-06)
- Complete migration to OpenRouter-based LLM architecture
- Versioned prompt system with markdown templates
- Multi-provider support with intelligent fallback
- All services migrated except browser-use (intentionally preserved)

### Legacy Code Cleanup (2025-07-05)
- Eliminated 900+ lines of legacy test execution code
- Migrated to modern V2 architecture with ExecutionOrchestrator
- Standardized result format across all execution types

### Service Separation (2025-07-05)
- Logging optimization with separate Logfire services
- `qak-api` service for main application logs
- `aery-browser` service for browser automation logs

## Development Workflow

### Memory Bank Integration
- Always read Memory Bank files when starting new sessions
- Update `activeContext.md` after significant changes
- Use Memory Bank context for understanding project decisions

### Testing Strategy
- No traditional unit tests - platform tests itself via browser automation
- Integration testing through live browser execution
- LLM-powered result validation

### Code Patterns
- Async/await patterns throughout (FastAPI + MongoDB)
- Factory pattern for LLM provider selection
- Strategy pattern for execution types
- Repository pattern for data access
- Clean architecture with service layer abstraction

## Troubleshooting

### Common Issues
- **EventBus conflicts**: Use unique task_id for each BrowserAgent instance
- **Screenshot loading**: Ensure artifact routes are properly configured
- **LLM rate limits**: Check Gemini API quota (250 requests/day on free tier)
- **Browser installation**: Run `playwright install` for browser automation

### Debug Commands
```bash
# Ensure virtual environment is activated first
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Test logging configuration
curl http://localhost:8000/test-logfire

# Check API health
curl http://localhost:8000/health

# View API documentation
open http://localhost:8000/docs
```

## Core Workflows & Behavior:

**1. Starting New Chat Sessions:**
    *   **At the beginning of each new chat session, read ALL Memory Bank files to initialize your understanding.**
    *   Check for the existence of all required `memory-bank/` files.
    *   If ANY file is missing, STOP. Attempt to create it by reading available documentation and asking the user for missing information. **Do not proceed without complete context.**
    *   Verify you have complete context before starting development.

**2. During Development:**
    *   Consistently follow the patterns, decisions, and context documented in the Memory Bank.
    *   **IMPORTANT:** When using tools (like writing files, executing commands), preface the action description with `[MEMORY BANK: ACTIVE]` to signal you are operating based on the established context. Example: `[MEMORY BANK: ACTIVE] I will now write the file...`
    *   Update Memory Bank files (especially `activeContext.md` and `progress.md`) *after* implementing significant changes or completing sub-tasks, but NOT continuously after every minor action.

**3. Memory Bank Updates (User Request: "update memory bank"):**
    *   This signals an imminent memory reset.
    *   Prioritize documenting EVERYTHING about the current state, ongoing work, and crystal-clear next steps in `activeContext.md` and `progress.md`.
    *   Complete the immediate task if possible before the reset.

**4. Plan Mode (User Request starts with "#plan"):**
    *   When a request begins with "#plan", enter plan-mode.
    *   In this mode, read all necessary files to understand the context, then create a detailed implementation plan WITHOUT making any actual changes to files or running commands.
    *   Structure your plan with clear steps, file paths, and specific code changes needed.
    *   At the end of the plan, ask if any adjustments are needed or if the user wants to execute the plan.
    *   If adjustments are requested, remain in plan-mode.
    *   Only exit plan-mode when explicitly instructed to execute the plan.

**5. External Documentation Access:**
    *   **Context7 MCP Integration**: This repository has Context7 MCP configured for accessing external technical documentation.
    *   Use Context7 when you need to obtain technical documentation for:
        - Framework-specific implementation details (FastAPI, Next.js, LangChain, browser-use, etc.)
        - API documentation for external libraries
        - Best practices for technologies used in the project
        - Troubleshooting specific technical issues
    *   Context7 provides real-time access to official documentation and should be used when the Memory Bank context is insufficient for technical implementation details.

**6. Aery MCP Workflows (AI-Powered Development Assistant):**
    *   **Aery Gemini MCP Integration**: This repository has Aery Gemini MCP configured for advanced AI-powered development workflows.
    *   **Available Workflows** (use these proactively to enhance development):
        - **`workflow_analyze_architecture`**: Comprehensive codebase architecture analysis
        - **`workflow_smart_code_review`**: Multi-perspective code review (security/performance/maintainability)
        - **`workflow_project_understanding`**: Deep project comprehension with business and technical context
        - **`workflow_context_manager`**: Smart context compression and persistent memory management
    
    *   **When to Use Aery MCP Workflows:**
        - **Project Onboarding**: Always run `workflow_project_understanding` and `workflow_analyze_architecture` when starting work on new areas
        - **Code Review**: Use `workflow_smart_code_review` before making significant changes or when reviewing user code
        - **Architecture Decisions**: Run `workflow_analyze_architecture` when making structural changes
        - **Context Management**: Use `workflow_context_manager` to compress long conversations or save important insights
        - **Memory Persistence**: Save analysis results to Aery's persistent memory for future sessions
    
    *   **Aery MCP Usage Patterns:**
        ```
        # Project Analysis Pattern
        1. "Use Aery to understand this project comprehensively"
        2. "Use Aery to analyze the architecture and save results"
        3. "Aery: save this analysis to memory for future reference"
        
        # Code Review Pattern  
        1. "Use Aery to do a complete code review of this component"
        2. "Aery: focus on security issues in this authentication code"
        3. "Run a performance analysis on this database query"
        
        # Context Management Pattern
        1. "Aery: compress our discussion and save key insights"
        2. "Use Aery to recall what we learned about the user authentication system"
        3. "Aery: save this configuration pattern for later use"
        ```
    
    *   **Aery MCP Best Practices:**
        - Always specify project paths as absolute paths when using architecture/project workflows
        - Use descriptive memory keys when saving analysis results
        - Leverage Aery's persistent memory across sessions for continuity
        - Combine workflows for comprehensive analysis (e.g., project understanding + architecture analysis)
        - Use context compression for long technical discussions
        - Proactively save important insights and patterns to Aery's memory

**General Guidance:**

*   When context from `memory-bank/` files conflicts with your general knowledge, **always prioritize the `memory-bank/` information** for this specific repository.
*   Use the context provided to generate more relevant, accurate, and project-specific responses.
    *   For technical implementation details not covered in the Memory Bank, leverage Context7 to access current official documentation.
    *   **Proactively use Aery MCP workflows** to enhance your understanding and provide better assistance:
        - Run project analysis workflows when working with unfamiliar code areas
        - Use code review workflows before suggesting changes or improvements
        - Leverage Aery's persistent memory to maintain context across sessions
        - Save important insights and patterns to Aery's memory for future reference
    *   Your ability to function effectively depends entirely on the accuracy and completeness of the Memory Bank and the intelligent use of available MCP tools. Maintain both diligently.

## 🚀 **Enhanced Capabilities with Aery MCP**

This repository is enhanced with **Aery Gemini MCP** which provides advanced AI-powered development workflows. When working in this repository, you have access to:

### **Core Aery Workflows:**
- **🏗️ Architecture Analysis**: Deep codebase structure and pattern analysis
- **📋 Smart Code Review**: Multi-perspective security, performance, and maintainability analysis  
- **🧠 Project Understanding**: Comprehensive technical and business context analysis
- **🗂️ Context Management**: Intelligent conversation compression and persistent memory

### **Workflow Integration Examples:**
```
# When user asks about project structure:
1. Use workflow_analyze_architecture to understand the codebase
2. Combine with Memory Bank context for complete picture
3. Save insights to Aery memory for future sessions

# When user shows code for review:
1. Use workflow_smart_code_review for comprehensive analysis
2. Cross-reference with project patterns from Memory Bank
3. Provide specific, contextual recommendations

# When starting new features:
1. Use workflow_project_understanding to grasp business context
2. Analyze architecture to understand integration points
3. Save development patterns to Aery memory
```

### **Memory Synergy:**
- **Memory Bank**: Project-specific context and decisions (this repository)
- **Aery MCP Memory**: Cross-session AI analysis and insights (persistent across restarts)
- **Combined Power**: Comprehensive understanding that grows over time

## IDE Configuration

The project includes extensive Cursor IDE rules in `.cursor/rules/` and GitHub Copilot instructions in `.github/copilot-instructions.md` for AI-assisted development.