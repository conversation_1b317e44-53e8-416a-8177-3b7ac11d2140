# System Patterns: AgentQA - Automatización de Pruebas con IA

## System Architecture

AgentQA implementa una arquitectura modular multicapa que separa responsabilidades y facilita el mantenimiento y escalabilidad.

```mermaid
graph TD
    subgraph "Frontend Layer"
        WEB[Next.js Web Interface]
        CLI[Python CLI Interface]
        API_CLIENT[API Client Libraries]
    end

    subgraph "API Layer"
        FASTAPI[FastAPI Application]
        PROJECT_ROUTES[Project Routes]
        SUITE_ROUTES[Suite Routes]
        TESTCASE_ROUTES[TestCase Routes]
        PROMPT_ROUTES[Prompt Routes]
        CONFIG_ROUTES[Config Routes]
        TRANSLATION_ROUTES[Translation Routes]
    end

    subgraph "Service Layer"
        TEST_SERVICE[TestService Core]
        PROMPT_SERVICE[PromptService]
        STORY_AGENT[StoryAgent]
        BROWSER_AGENT[BrowserAutomationAgent]
        PROJECT_MANAGER[ProjectManagerService]
    end

    subgraph "Integration Layer"
        BROWSER_USE[browser-use v0.5.0]
        DOM_PROCESSOR[DOM Processing System]
        HISTORY_PROCESSOR[History Tree Processor]
        MCP_SERVER[MCP Server]
        OBSERVABILITY[Observability & Telemetry]
        LANGCHAIN[LangChain Framework]
        AI_PROVIDERS[AI Providers: Gemini/OpenAI/Claude/Groq/Ollama]
    end

    subgraph "Storage Layer"
        PROJECT_FILES[JSON Project Files]
        PROMPT_TEMPLATES[Markdown Prompt Templates]
        TEST_HISTORY[Test Execution History]
        SCREENSHOTS[Screenshot Storage]
    end

    WEB --> FASTAPI
    CLI --> TEST_SERVICE
    API_CLIENT --> FASTAPI
    
    FASTAPI --> PROJECT_ROUTES
    FASTAPI --> SUITE_ROUTES
    FASTAPI --> TESTCASE_ROUTES
    FASTAPI --> PROMPT_ROUTES
    FASTAPI --> CONFIG_ROUTES
    FASTAPI --> TRANSLATION_ROUTES
    
    PROJECT_ROUTES --> TEST_SERVICE
    SUITE_ROUTES --> TEST_SERVICE
    TESTCASE_ROUTES --> TEST_SERVICE
    PROMPT_ROUTES --> PROMPT_SERVICE
    
    TEST_SERVICE --> STORY_AGENT
    TEST_SERVICE --> BROWSER_AGENT
    TEST_SERVICE --> PROJECT_MANAGER
    
    STORY_AGENT --> LANGCHAIN
    BROWSER_AGENT --> BROWSER_USE
    BROWSER_USE --> DOM_PROCESSOR
    BROWSER_USE --> HISTORY_PROCESSOR
    BROWSER_USE --> MCP_SERVER
    BROWSER_USE --> OBSERVABILITY
    BROWSER_USE --> AI_PROVIDERS
    DOM_PROCESSOR --> AI_PROVIDERS
    HISTORY_PROCESSOR --> AI_PROVIDERS
    MCP_SERVER --> AI_PROVIDERS
    LANGCHAIN --> AI_PROVIDERS
    
    PROJECT_MANAGER --> PROJECT_FILES
    BROWSER_AGENT --> TEST_HISTORY
    BROWSER_AGENT --> SCREENSHOTS
    PROMPT_SERVICE --> PROMPT_TEMPLATES

    style FASTAPI fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style TEST_SERVICE fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style BROWSER_USE fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style AI_PROVIDERS fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
```

## Key Technical Decisions

### **1. Arquitectura Modular por Capas**
- **API Layer**: Endpoints REST organizados por dominio (projects, suites, testcases)
- **Service Layer**: Lógica de negocio centralizada en servicios especializados
- **Integration Layer**: Abstracción de integraciones externas (IA, browser automation)
- **Storage Layer**: Persistencia basada en archivos JSON para simplicidad

### **2. Patrón Agent-Based Architecture**
- **StoryAgent**: Especializado en procesamiento de historias de usuario y generación de casos de prueba
- **BrowserAutomationAgent**: Dedicado a la ejecución de pruebas en navegadores reales
- **Separación de responsabilidades**: Cada agente tiene un dominio específico

### **3. Pipeline-Driven Workflow**
- **Full Pipeline**: User Story → Enhanced Story → Manual Tests → Gherkin → Code → Execution
- **Smoke Test Pipeline**: Direct description → Immediate execution
- **Transformación progresiva**: Cada etapa agrega valor y estructura

### **4. Multi-Provider AI Strategy**
- **Primary**: Google Gemini (GOOGLE_API_KEY)
- **Fallbacks**: OpenAI, Anthropic Claude, Groq
- **Configuración dinámica**: Permite cambiar proveedores sin modificar código

### **5. Framework-Agnostic Code Generation**
- **Soporta múltiples frameworks**: Selenium, Playwright, Cypress, Robot Framework
- **Templates dinámicos**: Generación basada en prompts estructurados
- **Extensibilidad**: Fácil agregar nuevos frameworks

## 2. Frontend Architecture (Next.js)

### **Component Structure**
```
web/src/
├── app/                     # App Router pages
│   ├── (dashboard)/         # Dashboard layout group
│   │   ├── page.tsx         # Main dashboard
│   │   ├── projects/        # Projects management
│   │   ├── qa-assistant/    # AI-powered QA tools
│   │   ├── smoke-test-playground/  # Test execution
│   │   ├── ai-tools/        # AI utilities
│   │   ├── prompts/         # Prompt management
│   │   └── settings/        # Configuration & browser setup
│   ├── globals.css          # Global styles
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Home redirect
├── components/              # Reusable UI components
│   ├── ui/                  # shadcn/ui base components
│   ├── sidebar.tsx          # Navigation sidebar
│   ├── project-form.tsx     # Project management
│   ├── browser-config.tsx   # Browser configuration
│   ├── prompt-editor.tsx    # Prompt editing interface
│   └── test-execution.tsx   # Test running interface
├── lib/                     # Utilities and services
│   ├── api.ts               # API client (fetch wrapper)
│   ├── utils.ts             # Helper functions
│   └── types.ts             # TypeScript definitions
└── hooks/                   # Custom React hooks
    └── use-api.ts           # TanStack Query hooks
```

### **Frontend Features Implemented**
*   **Complete Dashboard:** Main interface with navigation sidebar
*   **Project Management:** Full CRUD for test projects
*   **QA Assistant:** AI-powered tools for test generation
*   **Smoke Test Playground:** Interactive test execution interface
*   **Browser Configuration Manager:** Advanced browser settings
*   **Prompts Management:** Editor with ES↔EN translation
*   **AI Tools:** Collection of AI-powered utilities
*   **Settings:** Configuration panels and preferences

### **Design Patterns Used**
*   **App Router:** Next.js 15 App Router with layout groups
*   **Component Composition:** Reusable UI components with shadcn/ui
*   **Server Components:** Where appropriate for performance
*   **Client Components:** For interactive functionality
*   **Form Validation:** React Hook Form + Zod schemas
*   **State Management:** TanStack Query for server state
*   **Responsive Design:** Tailwind CSS with mobile-first approach
*   **API Integration:** Custom hooks with React Query for caching

## Design Patterns

### **1. Repository Pattern**
```python
# ProjectManagerService actúa como repository para proyectos
class ProjectManagerService:
    def get_project(self, project_id: str) -> Dict
    def create_project(self, project_data: Dict) -> Dict
    def update_project(self, project_id: str, updates: Dict) -> Dict
```

### **2. Service Layer Pattern**
```python
# TestService centraliza lógica de negocio
class TestService:
    def execute_test_case(self, project_id: str, suite_id: str, test_id: str) -> Dict
    def generate_code(self, framework: str, gherkin: str) -> str
```

### **3. Strategy Pattern**
```python
# Diferentes estrategias de configuración de browser
class BrowserConfigurations:
    @staticmethod
    def get_fast_config() -> BrowserHelperConfig
    def get_robust_config() -> BrowserHelperConfig
    def get_secure_config() -> BrowserHelperConfig
```

### **4. Template Method Pattern**
```python
# Pipeline de ejecución con pasos definidos
async def execute_full_pipeline(user_story: str):
    enhanced = await enhance_story(user_story)
    manual_tests = await generate_manual_tests(enhanced)
    gherkin = await generate_gherkin(manual_tests)
    code = await generate_code(gherkin)
    result = await execute_test(code)
    return result
```

### **5. Factory Pattern**
```python
# Factory para crear agentes según configuración
def create_agent(agent_type: str, config: Dict) -> Agent:
    if agent_type == "story":
        return StoryAgent(config)
    elif agent_type == "browser":
        return BrowserAutomationAgent(config)
```

### **6. Observer Pattern**
```python
# Notificaciones de estado de ejecución
class TestExecutionObserver:
    async def on_test_started(self, test_id: str)
    async def on_test_completed(self, test_id: str, result: Dict)
    async def on_test_failed(self, test_id: str, error: str)
```

## Component Relationships

### **Core Dependencies**
- **FastAPI** ← Routes ← Services ← Agents ← AI Providers
- **TestService** ← ProjectManager + StoryAgent + BrowserAgent
- **BrowserAgent** ← browser-use ← LangChain ← AI Models

### **Data Flow Patterns**
1. **Request Flow**: Frontend → API Routes → Services → Agents → AI/Browser
2. **Response Flow**: AI/Browser → Agents → Services → API Routes → Frontend
3. **Storage Flow**: Services → ProjectManager → JSON Files
4. **History Flow**: BrowserAgent → Test History → Screenshots

### **Configuration Hierarchy**
```
Environment Variables (.env)
    ↓
Default Configurations (BrowserConfigurations)
    ↓
Custom Configurations (JSON files)
    ↓
Runtime Configuration (API requests)
```

### **Error Handling Chain**
```
Browser Execution Errors → BrowserAgent
    ↓
Service Layer Validation → TestService
    ↓
API Error Responses → FastAPI Routes
    ↓
Frontend Error Display → React Components
```

### **Prompt Management**
```
Markdown Templates (prompts/) → PromptService
    ↓
Dynamic Prompt Generation → AI Agents
    ↓
Context-Aware Prompts → LangChain
    ↓
AI Model Responses → Structured Output
```

## Security & Performance Patterns

### **Security Measures**
- **API Key Management**: Secure storage in environment variables
- **CORS Configuration**: Controlled cross-origin access
- **Input Validation**: Pydantic models for request validation
- **Browser Security**: Configurable security settings for test environments

### **Performance Optimizations**
- **Async Operations**: FastAPI + async/await for concurrent execution
- **Connection Pooling**: Reuse of browser instances when possible
- **Caching Strategy**: Template caching for prompts
- **Resource Management**: Automatic cleanup of browser sessions

### **Scalability Considerations**
- **Modular Architecture**: Easy to scale individual components
- **Stateless Services**: Services can be replicated without state issues
- **File-based Storage**: Simple to backup and replicate
- **Configuration-driven**: Behavior configurable without code changes

## Browser-Use Advanced Patterns

### **1. DOM Processing Pattern**
```python
# Patrón para procesamiento inteligente del DOM
class DOMProcessingPattern:
    def __init__(self, dom_service: DomService):
        self.dom_service = dom_service
        self.clickable_processor = ClickableElementProcessor()
        self.history_processor = HistoryTreeProcessor()
    
    async def process_page_elements(self, page):
        # Extraer elementos clickeables
        clickable_elements = await self.dom_service.get_clickable_elements(page)
        
        # Generar hashes únicos para tracking
        element_hashes = self.clickable_processor.get_clickable_elements_hashes(dom_element)
        
        # Comparar con historial para detectar cambios
        for element in clickable_elements:
            history_element = self.history_processor.convert_dom_element_to_history_element(element)
            is_changed = self.history_processor.compare_history_element_and_dom_element(
                history_element, element
            )
        
        return {
            "clickable_elements": clickable_elements,
            "element_hashes": element_hashes,
            "changes_detected": is_changed
        }
```

### **2. Multi-LLM Fallback Pattern**
```python
# Patrón para fallback automático entre proveedores de LLM
class MultiLLMPattern:
    def __init__(self):
        self.providers = {
            "primary": ChatOpenAI(model="gpt-4"),
            "fallback_1": ChatAnthropic(model="claude-3-sonnet"),
            "fallback_2": ChatGoogle(model="gemini-pro"),
            "local": ChatOllama(model="llama2")
        }
    
    async def execute_with_fallback(self, prompt: str, max_retries: int = 3):
        for provider_name, provider in self.providers.items():
            try:
                response = await provider.ainvoke(prompt)
                return {
                    "response": response,
                    "provider_used": provider_name,
                    "success": True
                }
            except Exception as e:
                logger.warning(f"Provider {provider_name} failed: {e}")
                continue
        
        raise Exception("All LLM providers failed")
```

### **3. Browser Profile Configuration Pattern**
```python
# Patrón para configuración adaptativa de perfiles de navegador
class BrowserProfilePattern:
    def __init__(self):
        self.profile_configs = {
            "development": self._get_dev_profile,
            "testing": self._get_test_profile,
            "production": self._get_prod_profile,
            "debugging": self._get_debug_profile
        }
    
    def get_adaptive_profile(self, environment: str, requirements: Dict) -> BrowserProfile:
        base_profile = self.profile_configs[environment]()
        
        # Detectar configuración de pantalla automáticamente
        base_profile.detect_display_configuration()
        
        # Aplicar configuraciones específicas según requisitos
        if requirements.get("headless"):
            base_profile.headless = True
        
        if requirements.get("mobile_emulation"):
            base_profile.viewport = ViewportSize(width=375, height=667)
            base_profile.device_scale_factor = 2.0
        
        return base_profile
    
    def _get_dev_profile(self) -> BrowserProfile:
        return BrowserProfile(
            headless=False,
            channel=BrowserChannel.CHROME,
            extra_http_headers={"X-Dev-Mode": "true"},
            permissions=["geolocation", "notifications"]
        )
```

### **4. MCP Integration Pattern**
```python
# Patrón para integración con Model Context Protocol
class MCPIntegrationPattern:
    def __init__(self, browser_session: BrowserSession):
        self.browser_session = browser_session
        self.mcp_server = MCPServer(
            port=8080,
            browser_config=browser_session.profile,
            enable_vision=True,
            security_mode="strict"
        )
    
    async def expose_browser_capabilities(self):
        """Expone capacidades del navegador como herramientas MCP"""
        capabilities = {
            "navigate": self._navigate_tool,
            "click": self._click_tool,
            "extract_content": self._extract_content_tool,
            "screenshot": self._screenshot_tool,
            "manage_tabs": self._manage_tabs_tool
        }
        
        for tool_name, tool_func in capabilities.items():
            await self.mcp_server.register_tool(tool_name, tool_func)
        
        return await self.mcp_server.start()
    
    async def _navigate_tool(self, url: str) -> Dict:
        page = await self.browser_session.get_current_page()
        await page.goto(url)
        return {"status": "success", "url": url, "title": await page.title()}
```

### **5. Observability Pattern**
```python
# Patrón para observabilidad y telemetría
class ObservabilityPattern:
    def __init__(self, enable_telemetry: bool = True, enable_debug: bool = False):
        self.telemetry_service = TelemetryService() if enable_telemetry else None
        self.debug_mode = enable_debug
    
    @observe_debug(name="browser_action", span_type="TOOL")
    async def execute_browser_action(self, action: str, **kwargs):
        """Ejecuta acción del navegador con observabilidad completa"""
        start_time = time.time()
        
        try:
            # Registrar inicio de acción
            if self.telemetry_service:
                await self.telemetry_service.capture_event("action_started", {
                    "action": action,
                    "timestamp": start_time
                })
            
            # Ejecutar acción
            result = await self._execute_action(action, **kwargs)
            
            # Registrar éxito
            execution_time = time.time() - start_time
            if self.telemetry_service:
                await self.telemetry_service.capture_event("action_completed", {
                    "action": action,
                    "execution_time": execution_time,
                    "success": True
                })
            
            return result
            
        except Exception as e:
            # Registrar error
            if self.telemetry_service:
                await self.telemetry_service.capture_event("action_failed", {
                    "action": action,
                    "error": str(e),
                    "execution_time": time.time() - start_time
                })
            raise
```

### **6. File System Integration Pattern**
```python
# Patrón para integración avanzada con sistema de archivos
class FileSystemIntegrationPattern:
    def __init__(self, base_path: Path):
        self.file_system = FileSystem(base_path)
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.allowed_extensions = {".txt", ".md", ".json", ".csv", ".log"}
    
    async def process_test_artifacts(self, test_execution_id: str) -> Dict:
        """Procesa artefactos generados durante la ejecución de pruebas"""
        artifacts = {
            "screenshots": [],
            "logs": [],
            "reports": [],
            "metadata": {}
        }
        
        # Listar archivos del sistema
        files = self.file_system.list_files()
        
        for file_name in files:
            if not self._is_valid_filename(file_name):
                continue
            
            file_content = self.file_system.read_file(file_name)
            file_type = self._classify_file_type(file_name)
            
            artifacts[file_type].append({
                "name": file_name,
                "size": len(file_content),
                "content_preview": file_content[:200] if len(file_content) > 200 else file_content
            })
        
        # Generar descripción del sistema de archivos
        artifacts["metadata"]["system_description"] = self.file_system.describe()
        
        return artifacts
    
    def _is_valid_filename(self, filename: str) -> bool:
        """Valida nombre de archivo según reglas del sistema"""
        if not filename or len(filename) > 255:
            return False
        
        name, extension = self.file_system._parse_filename(filename)
        return extension in self.allowed_extensions
```
